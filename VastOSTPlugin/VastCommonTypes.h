// VastCommonTypes.h
// Common data types and definitions for the Vast OST plugin

#ifndef _VAST_COMMON_TYPES_H_
#define _VAST_COMMON_TYPES_H_

#include <string>
#include <vector>
#include <cstdint>
#include <map>

// Use SDK-provided platform-specific export macros instead of custom ones
// The SDK provides STS_EXPORT in stsplat.h which handles platform differences correctly

// Forward declarations
class VastStorageServer;

// Operation ID structure for async operations
struct stsp_opid_s {
    VastStorageServer* vast_server;
    int operation_type;  // 1=read, 2=write, 3=copy, etc.
    bool completed;
    int result_code;
    sts_uint64_t bytes_transferred;
};
class VastLSU;
class VastImage;

// Common error codes (if not defined in OST SDK)
#ifndef STS_LSS_OK
#define STS_LSS_OK 0
#endif

// Version constants
#define STS_MINIMUM_VERSION 10
#define STS_IT_FULL 1

// Open modes (if not defined in OST SDK)
#ifndef STS_O_READ
#define STS_O_READ 0x0001
#define STS_O_WRITE 0x0002
#define STS_O_RDWR (STS_O_READ | STS_O_WRITE)
#endif

// Create flags (if not defined in OST SDK)
#ifndef STS_CIF_FORCEREPLACE
#define STS_CIF_FORCEREPLACE 0x0001
#define STS_CIF_LARGEOBJ     0x0002
#define STS_CIF_COMPLETE     0x0004
#define STS_CIF_NOMETA       0x0008
#endif

// Image status flags (if not defined in OST SDK)
#ifndef STS_II_IMAGE_CREATED
#define STS_II_IMAGE_CREATED  0x0001
#endif

#ifndef STS_II_IMAGE_COMPLETE
#define STS_II_IMAGE_COMPLETE 0x0004
#endif

// Image status flags (if not defined in OST SDK)
#ifndef STS_II_FILES_CREATED
#define STS_II_FILES_CREATED  0x0002
#endif
// Image status flags (if not defined in OST SDK)
#ifndef STS_II_IMAGE_PENDING
#define STS_II_IMAGE_PENDING  0x0008
#endif

// File types (if not defined in OST SDK)
#ifndef STS_FT_DISK
#define STS_FT_DISK  1
#define STS_FT_INCR  2
#define STS_FT_FULL  3
#endif

// Error codes (if not defined in OST SDK)
#ifndef STS_EOK
#define STS_EOK 0
#endif

#ifndef STS_ENOTOPEN
#define STS_ENOTOPEN 1030
#endif

#ifndef STS_ENOTSUP
#define STS_ENOTSUP 1040
#endif

#ifndef STS_EINVAL
#define STS_EINVAL 1050
#endif

#ifndef STS_EEXIST
#define STS_EEXIST 1060
#endif

#ifndef STS_ENOENT
#define STS_ENOENT 1070
#endif

#ifndef STS_EBUSY
#define STS_EBUSY 1080
#endif

#ifndef STS_EINTERNAL
#define STS_EINTERNAL 1090
#endif

// Define block size constant (if not defined in OST SDK)
#ifndef STS_BLOCK_SIZE
#define STS_BLOCK_SIZE (64 * 1024)
#endif

// Missing NetBackup constants and types
typedef struct stsnbu_bckpid_v65_s {
    char bi_master_server[256];
    uint64_t bi_time;
    int bi_copy_number;
} stsnbu_bckpid_v65_t;

typedef struct stsnbu_isinfo_v65_s {
    stsnbu_bckpid_v65_t isi_bckpid;
    int isi_strm_num;
    int isi_frag_num;
} stsnbu_isinfo_v65_t;

// Image related definitions
struct ImageDefinition {
    std::string basename;
    std::string date;
    std::string fulldate;
    uint64_t size;
    std::string name;  // Added for VastRestClient
    std::string policy; // Added for VastRestClient
};

// Our own implementation of ImageInfo - replacing dependency on sample code
struct ImageInfo {
    uint64_t size;
    std::string name;
    std::string date;
    std::string image_path;
    ImageDefinition idef;
};

// LSU related definitions
struct LSUDefinition {
    std::string name;
    uint64_t max_transfer;
    uint64_t block_size;
    uint32_t lsu_flags;
    uint32_t lsu_rep_sources;
    uint32_t lsu_rep_targets;
};

struct LSUInfo {
    LSUDefinition ldef;
    uint64_t lsu_capacity;
    uint64_t lsu_capacity_phys;
    uint64_t lsu_used;
    uint64_t lsu_used_phys;
    uint64_t num_images;
};

// S3 Operation Defines
struct VastS3ObjectInfo {
    std::string key;
    std::string etag;
    uint64_t size;
    std::string last_modified;
    std::string content_type;
    std::map<std::string, std::string> metadata;  // Added for VastS3Client
};

struct VastS3BucketInfo {
    std::string name;
    std::string creation_date;
};

struct VastS3KeyPair {
    std::string access_key;
    std::string secret_key;
    std::string user_name;
    bool enabled; // Added for VastRestClient
};

struct VastS3PartInfo {
    int part_number;
    std::string etag;
    uint64_t size;
};

struct VastS3MultipartUpload {
    std::string upload_id;
    std::string key;
    std::vector<VastS3PartInfo> parts;
    uint64_t total_size;
};

// HTTP Response structure
struct S3Response {
    int status_code;
    std::string body;
    std::string error_message;
    FILE* output_file;  // Added for VastS3Client
    std::map<std::string, std::string> headers;  // Added for VastS3Client
    int response_code;  // Added for VastS3Client
};

// REST API types
struct VastAuthTokens {
    std::string access_token;
    std::string refresh_token;
    int expires_in;
    std::string token_type;
    std::string user_type; // Added for VastRestClient
    time_t expires_at; // Added for VastRestClient
};

struct VastApiResponse {
    int status_code;
    std::string body;
    std::string error_message;
    std::map<std::string, std::string> headers;
    bool success; // Added for VastRestClient
};

struct VastViewInfo {
    std::string name;
    std::string path;
    std::string protocol;  // "nfs" or "smb"
    uint64_t capacity;
    uint64_t used;
    std::string status;
    std::map<std::string, std::string> properties;
    
    // Added fields for VastRestClient
    std::string tenant_name;
    std::string policy_name;
    bool nfs_enabled;
    bool smb_enabled;
    bool s3_enabled;
    uint64_t capacity_bytes;
    uint64_t used_bytes;
};

struct VastS3Key {
    std::string name;
    std::string access_key; 
    std::string user_name;
    std::string creation_date;
    
    // Added fields for VastRestClient
    std::string secret_key;
    std::string tenant_name;
    bool enabled;
    std::string created_at;
};

struct VastTenantInfo {
    std::string name;
    std::string description;
    std::string quota;  // Optional quota
    std::vector<std::string> users;
    std::map<std::string, std::string> properties;
    
    // Added fields for VastRestClient
    std::string guid;
    std::string default_vip_pool;
    std::string encryption_group;
    bool ssd_enabled;
    bool trash_enabled;
    uint64_t capacity_bytes;
    uint64_t used_bytes;
    std::string created_at;
};

#endif // _VAST_COMMON_TYPES_H_