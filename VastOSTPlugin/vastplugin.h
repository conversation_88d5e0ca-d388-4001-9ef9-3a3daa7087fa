/*
 *************************************************************************
 * Vast Data Plugin Header
 * UPDATED: Now follows OST SDK plugin structure requirements
 *************************************************************************
 */

#ifndef _VAST_PLUGIN_H_
#define _VAST_PLUGIN_H_

#include "stspi.h"
#include "stsi.h"
#include "VastCommonTypes.h"
#include "VastStorageServer.h"
#include "VastLSU.h"
#include "VastImage.h"

// Use SDK-defined constants instead of custom ones
// STS_EEOF is not a standard SDK constant - use STS_ENOENT for "no more entries"
// All other constants should come from SDK headers

// Forward declare structs
struct stsp_server_handle_s {
    const sts_session_def_v7_t* session;
    VastStorageServer* vast_server;
    char server_name[256];
};

struct stsp_lsu_list_handle_s {
    stsp_server_handle_t server_handle;
    size_t cursor;
    std::vector<VastLSU> lsu_list;  // Change from pointer to direct vector
};

struct stsp_image_handle_s {
    stsp_server_handle_t server_handle;
    stsp_lsu_t lsu;
    sts_image_def_v10_t image_def;  // Rename from img_def to match implementation
    VastImage* vast_image;  // Rename from target_image to match implementation
};

struct stsp_image_list_handle_s {
    stsp_server_handle_t server_handle;
    stsp_lsu_t lsu;
    size_t cursor;
    int type;
    std::vector<VastImage> image_list;  // Change from pointer to direct vector
};

struct stsp_evc_handle_s {
    stsp_server_handle_t server_handle;
    int flags;
    int mode;
    sts_evseqno_v11_t sequence;
    sts_evhandler_v11_t handler;
    sts_event_v11_t* event;
};

struct stsp_opid_s {
    VastStorageServer* vast_server;
    int operation_id;
    void* buffer;
    sts_uint64_t length;
    sts_uint64_t offset;
};

/*********************************************************************************
 * Core Plugin Function Declarations (Required by OST SDK)
 *********************************************************************************/

extern "C" {

// Plugin initialization and termination
int stspi_init(sts_uint64_t masterVersion, const char* path, stspi_api_t* stspAPI);
int stspi_terminate();
int stspi_claim(const sts_server_name_v7_t serverName);

// Server management
int stspi_open_server(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t sts_server_name,
    const sts_cred_v7_t* credentials,
    const sts_interface_v7_t stsInterface,
    stsp_server_handle_t* sh);
int stspi_close_server(stsp_server_handle_t sh);
int stspi_get_server_prop(stsp_server_handle_t sh, sts_server_info_v8_t* serverInfo);
int stspi_get_server_prop_byname(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t serverName,
    sts_server_info_v8_t* serverInfo);

// LSU management - Fix parameter types to match v11
int stspi_open_lsu_list_v11(
    stsp_server_handle_t sh,
    const sts_lsu_def_v11_t* lsudef,
    stsp_lsu_list_handle_t* lsu_list_handle);
int stspi_list_lsu(stsp_lsu_list_handle_t lsuListHandle, sts_lsu_name_t* lsuName);
int stspi_close_lsu_list(const stsp_lsu_list_handle_t lsuListHandle);
int stspi_get_lsu_prop_byname_v11(const stsp_lsu_t* lsu, sts_lsu_info_v11_t* lsuInfo);
int stspi_label_lsu(const stsp_lsu_t* lsu, sts_lsu_label_t lsu_label);

// Image management - Ensure v10 consistency
int stspi_create_image_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    int pendingFlag,
    stsp_image_handle_t* imageHandle);
int stspi_open_image_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    int mode,
    stsp_image_handle_t* imageHandle);
int stspi_close_image(stsp_image_handle_t ih, int completeFlag, int forceFlag);
int stspi_delete_image_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    int asyncFlag);

// Image I/O operations
int stspi_read_image(
    stsp_image_handle_t ih,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* bytesread);
int stspi_write_image(
    stsp_image_handle_t ih,
    sts_stat_t* stat,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* byteswritten);
int stspi_flush_image(stsp_image_handle_t image_handle);

// Asynchronous operations
int stspi_async_read_image_v11(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    stsp_opid_t* opid);

int stspi_async_write_image_v11(
    stsp_image_handle_t image_handle,
    sts_stat_v7_t* stat,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    stsp_opid_t* opid);

int stspi_async_wait_v11(
    const sts_session_def_v7_t* sd,
    stsp_opid_t opid,
    int blockflag,
    sts_aioresult_v11_t* result);

// Copy operations
int stspi_copy_image_v11(
    const stsp_lsu_v7_t* to_lsu,
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_v7_t* from_lsu,
    const sts_image_def_v10_t* from_img,
    const sts_opname_v11_t imageset,
    int eventflag);

int stspi_async_copy_image_v11(
    const stsp_lsu_v7_t* to_lsu,
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_v7_t* from_lsu,
    const sts_image_def_v10_t* from_img,
    stsp_opid_t* opid,
    const sts_opname_v11_t imageset,
    int eventflag);

// Image list operations
int stspi_open_image_list_v10(
    const stsp_lsu_t* lsu,
    int type,
    stsp_image_list_handle_t* image_list_handle);
int stspi_list_image_v10(
    stsp_image_list_handle_t image_list_handle,
    sts_image_def_v10_t* img);
int stspi_close_image_list(stsp_image_list_handle_t image_list_handle);

// Metadata operations
int stspi_read_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* bytesread);
int stspi_write_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* byteswritten);

// Event channel operations
int stspi_open_evchannel_v11(
    const sts_session_def_v7_t* sd,
    const sts_server_name_v7_t server,
    const sts_cred_v7_t* cred,
    const sts_interface_v7_t iface,
    sts_evhandler_v11_t handler,
    sts_event_v11_t* event,
    int flags,
    sts_evseqno_v11_t evseqno,
    stsp_evc_handle_t* pevc_handle);

int stspi_close_evchannel_v9(stsp_evc_handle_t evc_handle);
int stspi_get_event_v11(stsp_evc_handle_t evc_handle, sts_event_v11_t* event);
int stspi_get_image_prop_byname_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    sts_image_info_v10_t* imageInfo);

int stspi_get_image_prop_v10(
    stsp_image_handle_t image_handle,
    sts_image_info_v10_t* image_info);

} // extern "C"

#endif /* _VAST_PLUGIN_H_ */