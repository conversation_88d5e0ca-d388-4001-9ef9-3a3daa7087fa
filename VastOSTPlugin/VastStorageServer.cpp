/*
 *************************************************************************
 * Vast Data Storage Server Implementation
 * Handles communication with Vast Data VMS REST API and S3 operations
 *************************************************************************
 */

#include "VastStorageServer.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include "VastLSU.h"
#include "VastImage.h"  // Add this include to fix incomplete type issue

// Include OST SDK headers - these will automatically include the correct platform headers
#include "stspi.h"
#include "stsi.h"

#include <iostream>
#include <sstream>
#include <cstring>
#include <memory>

// Error code definitions - using OST SDK constants
#ifndef STS_ERR_NOT_CONNECTED
#define STS_ERR_NOT_CONNECTED STS_ECONNECT
#endif

#ifndef STS_ERR_AUTH_FAILED
#define STS_ERR_AUTH_FAILED STS_EAUTH
#endif

#ifndef STS_ERR_BUFFER_TOO_SMALL
#define STS_ERR_BUFFER_TOO_SMALL STS_EMALLOC
#endif

#ifndef STS_ERR_NOT_SUPPORTED
#define STS_ERR_NOT_SUPPORTED STS_ENOTSUP
#endif

#ifndef STS_ERR_CONNECT
#define STS_ERR_CONNECT STS_ECONNECT
#endif

#ifndef STS_ERR_INVALID_PARAMETER
#define STS_ERR_INVALID_PARAMETER STS_EINVAL
#endif

#ifndef STS_ERR_INTERNAL
#define STS_ERR_INTERNAL STS_EINTERNAL
#endif

VastStorageServer::VastStorageServer()
    : m_connected(false)
    , m_last_error(STS_EOK)
{
    clearError();
}

VastStorageServer::~VastStorageServer()
{
    disconnect();
    
    // Clean up LSU cache
    for (auto& pair : m_lsu_cache) {
        delete pair.second;
    }
    m_lsu_cache.clear();
}

int VastStorageServer::connect(const std::string& server_name,
                              const std::string& username,
                              const std::string& password,
                              const std::string& vms_endpoint,
                              const std::string& s3_endpoint)
{
    std::cout << "VastStorageServer: Connecting to " << server_name << std::endl;
    
    clearError();
    
    if (m_connected) {
        std::cout << "VastStorageServer: Already connected" << std::endl;
        return STS_EOK;
    }

    try {
        // Store connection parameters
        m_server_name = server_name;
        m_username = username;
        m_password = password;
        m_vms_endpoint = vms_endpoint;
        m_s3_endpoint = s3_endpoint;

        // Initialize REST client for VMS API
        m_rest_client.reset(new VastRestClient());
        int result = m_rest_client->initialize(m_vms_endpoint);
        if (result != STS_EOK) {
            setError(result, "Failed to initialize VMS REST client");
            return result;
        }

        // Authenticate to VMS
        result = authenticateToVms();
        if (result != STS_EOK) {
            setError(result, "VMS authentication failed");
            return result;
        }

        // Initialize S3 client
        result = initializeS3Client();
        if (result != STS_EOK) {
            setError(result, "Failed to initialize S3 client");
            return result;
        }

        m_connected = true;
        std::cout << "VastStorageServer: Successfully connected to Vast Data cluster" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Connection failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::disconnect()
{
    std::cout << "VastStorageServer: Disconnecting from server" << std::endl;
    
    if (!m_connected) {
        return STS_EOK;
    }

    try {
        // Clean up clients
        if (m_s3_client) {
            m_s3_client->cleanup();
            m_s3_client.reset();
        }
        
        if (m_rest_client) {
            m_rest_client->cleanup();
            m_rest_client.reset();
        }

        // Clear authentication tokens
        m_auth_token.clear();
        m_refresh_token.clear();
        
        m_connected = false;
        clearError();
        
        std::cout << "VastStorageServer: Successfully disconnected" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Disconnect failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerInfo(sts_server_info_v8_t* server_info)
{
    if (!server_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Fill in server information using v8 structure
        strncpy(server_info->srv_server, m_server_name.c_str(), sizeof(server_info->srv_server) - 1);
        server_info->srv_server[sizeof(server_info->srv_server) - 1] = '\0';

        // Set server flags and capabilities
        server_info->srv_flags = STS_SRV_IMAGELIST | STS_SRV_CRED | STS_SRV_CONRW;
        server_info->srv_maxconnect = 10;
        server_info->srv_nconnect = 0;

        // Initialize stream format handlers (placeholder)
        for (int i = 0; i < STS_MAX_STH; i++) {
            memset(server_info->srv_sth[i], 0, sizeof(server_info->srv_sth[i]));
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get server info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

// Implementation of missing methods
int VastStorageServer::getLSUInfo(const std::string& lsu_name, sts_lsu_info_v9_t* lsu_info)
{
    if (!lsu_info) {
        return STS_EINVAL;
    }

    // For now, provide basic LSU info - in real implementation this would query VMS
    memset(lsu_info, 0, sizeof(sts_lsu_info_v9_t));

    // Fill basic LSU information - fix structure access based on actual SDK structure
    strncpy(lsu_info->lsu_server, m_server_name.c_str(), sizeof(lsu_info->lsu_server) - 1);
    // Note: The actual structure may not have nested lsu_name field, adjust based on SDK
    // For now, just set what we can access directly
    
    // Set default capacity values (would come from VMS in real implementation)
    lsu_info->lsu_capacity = 1024ULL * 1024 * 1024 * 1024; // 1TB default
    lsu_info->lsu_capacity_phys = lsu_info->lsu_capacity;
    lsu_info->lsu_used = 0;
    lsu_info->lsu_used_phys = 0;
    lsu_info->lsu_images = 0;

    return STS_EOK;
}

VastLSU* VastStorageServer::getLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Getting LSU: " << lsu_name << std::endl;
    
    // Check cache first
    auto it = m_lsu_cache.find(lsu_name);
    if (it != m_lsu_cache.end()) {
        std::cout << "VastStorageServer: Found LSU in cache: " << lsu_name << std::endl;
        return it->second;
    }

    if (!m_connected || !m_s3_client) {
        std::cerr << "VastStorageServer: Not connected to server or S3 client not available" << std::endl;
        return nullptr;
    }

    try {
        // Verify the bucket exists on Vast Data using S3 API
        std::cout << "VastStorageServer: Verifying bucket exists: " << lsu_name << std::endl;
        int bucket_result = m_s3_client->headBucket(lsu_name);
        if (bucket_result != 0) {
            std::cerr << "VastStorageServer: Bucket/LSU does not exist: " << lsu_name << std::endl;
            return nullptr;
        }

        // Get bucket metadata and capacity information
        VastS3BucketInfo bucket_info;
        // Use headBucket instead of non-existent getBucketInfo method
        bucket_result = m_s3_client->headBucket(lsu_name);
        if (bucket_result != 0) {
            std::cout << "VastStorageServer: Warning - Could not get detailed bucket info for: " << lsu_name << std::endl;
            // Continue with basic LSU creation even if detailed info fails
            bucket_info.name = lsu_name;
            bucket_info.creation_date = "";
            // Remove references to non-existent fields
        } else {
            bucket_info.name = lsu_name;
            bucket_info.creation_date = "";
        }

        // Create new LSU object with real data from Vast
        VastLSU* lsu = new VastLSU(lsu_name, this);
        
        // Initialize the LSU with the bucket name
        if (lsu->initialize(lsu_name) != STS_EOK) {
            std::cerr << "VastStorageServer: Failed to initialize LSU: " << lsu_name << std::endl;
            delete lsu;
            return nullptr;
        }

        // Update LSU with real capacity information if available
        // Remove check for non-existent size field
        lsu->updateInfo();

        // Cache the LSU
        m_lsu_cache[lsu_name] = lsu;
        std::cout << "VastStorageServer: Successfully created and cached LSU: " << lsu_name << std::endl;
        
        return lsu;
    }
    catch (const std::exception& e) {
        std::cerr << "VastStorageServer: Exception while getting LSU " << lsu_name << ": " << e.what() << std::endl;
        return nullptr;
    }
}

std::string VastStorageServer::getAccessKey() const
{
    return m_access_key;
}

std::string VastStorageServer::getSecretKey() const
{
    return m_secret_key;
}

// Async operation implementations
int VastStorageServer::startAsyncRead(const std::string& lsu_name, const VastImage& image, void* buf, sts_uint64_t length, sts_uint64_t offset, stsp_opid_t* opid)
{
    std::cout << "VastStorageServer: Starting async read operation" << std::endl;

    try {
        // For now, perform synchronous read and return immediately
        // In a full implementation, this would start an async operation
        sts_uint64_t bytes_read = 0;
        int result = readImageData(lsu_name, image, buf, length, offset, &bytes_read);

        if (result == STS_EOK && opid) {
            // Create a simple operation ID structure
            *opid = new stsp_opid_s();
            (*opid)->vast_server = this;
            (*opid)->operation_type = 1; // read operation
            (*opid)->completed = true;
            (*opid)->result_code = result;
            (*opid)->bytes_transferred = bytes_read;
        }

        return result;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Async read failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::startAsyncWrite(const std::string& lsu_name, const VastImage& image, void* buf, sts_uint64_t length, sts_uint64_t offset, stsp_opid_t* opid)
{
    std::cout << "VastStorageServer: Starting async write operation" << std::endl;

    try {
        // For now, perform synchronous write and return immediately
        // In a full implementation, this would start an async operation
        sts_uint64_t bytes_written = 0;
        int result = writeImageData(lsu_name, image, buf, length, offset, &bytes_written);

        if (result == STS_EOK && opid) {
            // Create a simple operation ID structure
            *opid = new stsp_opid_s();
            (*opid)->vast_server = this;
            (*opid)->operation_type = 2; // write operation
            (*opid)->completed = true;
            (*opid)->result_code = result;
            (*opid)->bytes_transferred = bytes_written;
        }

        return result;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Async write failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::waitForAsyncOperation(stsp_opid_t opid, int blockFlag, sts_aioresult_v11_t* result)
{
    std::cout << "VastStorageServer: Waiting for async operation" << std::endl;

    if (!opid || !result) {
        return STS_EINVAL;
    }

    try {
        // Since our current implementation is synchronous, operations complete immediately
        result->aio_result = opid->result_code;
        result->aio_bytes = opid->bytes_transferred;
        result->aio_opid = opid;

        // Clean up the operation ID
        delete opid;

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Async wait failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::startAsyncImageDelete(const std::string& lsu_name, const std::string& image_name)
{
    std::cout << "VastStorageServer: Starting async image delete for " << image_name << std::endl;

    try {
        // For now, perform synchronous delete
        // In a full implementation, this would start an async operation
        return deleteImage(lsu_name, image_name);
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Async image delete failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::startAsyncCopyImage(const std::string& src_lsu_name, const std::string& src_image_name,
                                          const std::string& dst_lsu_name, const std::string& dst_image_name,
                                          stsp_opid_t* opid, const char* imageset)
{
    std::cout << "VastStorageServer: Starting async copy image operation" << std::endl;

    try {
        // For now, perform synchronous copy and return immediately
        // In a full implementation, this would start an async operation
        int result = copyImage(src_lsu_name, src_image_name, dst_lsu_name, dst_image_name, imageset);

        if (result == STS_EOK && opid) {
            // Create a simple operation ID structure
            *opid = new stsp_opid_s();
            (*opid)->vast_server = this;
            (*opid)->operation_type = 3; // copy operation
            (*opid)->completed = true;
            (*opid)->result_code = result;
            (*opid)->bytes_transferred = 0; // Copy operations don't track bytes
        }

        return result;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Async copy failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getLSUList(std::vector<VastLSU>& lsu_list)
{
    std::cout << "VastStorageServer: Getting LSU list from Vast Data" << std::endl;
    
    clearError();
    lsu_list.clear();
    
    if (!m_connected || !m_s3_client) {
        setError(STS_ECONNECT, "Not connected to storage server or S3 client not initialized");
        return STS_ECONNECT;
    }
    
    try {
        // Get actual bucket list from Vast Data S3 API
        std::vector<VastS3BucketInfo> buckets;
        int result = m_s3_client->listBuckets(buckets);
        if (result != STS_EOK) {
            setError(result, "Failed to list S3 buckets from Vast Data");
            return result;
        }

        std::cout << "VastStorageServer: Found " << buckets.size() << " buckets on Vast Data" << std::endl;

        // Convert S3 buckets to LSUs
        for (const auto& bucket : buckets) {
            // Create LSU object for each bucket
            VastLSU lsu(bucket.name, this);
            result = lsu.initialize(bucket.name);
            if (result == STS_EOK) {
                lsu_list.push_back(lsu);
                std::cout << "VastStorageServer: Added LSU: " << bucket.name << std::endl;
            } else {
                std::cerr << "VastStorageServer: Failed to initialize LSU for bucket: " << bucket.name << std::endl;
            }
        }
        
        std::cout << "VastStorageServer: Successfully retrieved " << lsu_list.size() << " LSUs" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to get LSU list: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastStorageServer::getLSUInfo(const std::string& lsu_name, sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    if (!m_connected || !m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Check cache first
        auto it = m_lsu_cache.find(lsu_name);
        VastLSU* lsu = nullptr;
        
        if (it != m_lsu_cache.end()) {
            lsu = it->second;
        } else {
            // Create LSU if not found in cache
            // Check if bucket exists on Vast Data first
            if (m_s3_client) {
                int bucket_result = m_s3_client->headBucket(lsu_name);
                if (bucket_result != 0) {
                    setError(STS_ERR_INVALID_PARAMETER, "LSU/bucket not found: " + lsu_name);
                    return STS_ERR_INVALID_PARAMETER;
                }
            }

            lsu = new VastLSU(lsu_name, this);
            if (lsu->initialize(lsu_name) != STS_EOK) {
                delete lsu;
                setError(STS_ERR_INTERNAL, "Failed to initialize LSU");
                return STS_ERR_INTERNAL;
            }
            m_lsu_cache[lsu_name] = lsu;
        }

        // Fill in LSU info structure using getLSUInfo method
        return lsu->getLSUInfo(lsu_info);
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get LSU info: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::createLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Creating LSU (S3 bucket): " << lsu_name << std::endl;

    if (!m_connected || !m_rest_client || !m_s3_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Check if LSU already exists in cache
        auto cache_it = m_lsu_cache.find(lsu_name);
        if (cache_it != m_lsu_cache.end()) {
            std::cout << "VastStorageServer: LSU " << lsu_name << " already exists" << std::endl;
            return STS_EOK;
        }

        // Create S3 bucket on Vast Data
        std::string bucket_name = lsu_name;
        int result = m_s3_client->createBucket(bucket_name);
        if (result != 0) {
            // Check if bucket already exists
            result = m_s3_client->headBucket(bucket_name);
            if (result != 0) {
                setError(STS_ERR_INTERNAL, "Failed to create S3 bucket: " + bucket_name);
                return STS_ERR_INTERNAL;
            }
            std::cout << "VastStorageServer: Bucket " << bucket_name << " already exists" << std::endl;
        } else {
            std::cout << "VastStorageServer: Successfully created S3 bucket: " << bucket_name << std::endl;
        }

        // Create LSU object for the bucket
        VastLSU* lsu = new VastLSU(lsu_name, this);
        if (lsu->initialize(bucket_name) == STS_EOK) {
            m_lsu_cache[lsu_name] = lsu;
            std::cout << "VastStorageServer: Successfully created LSU: " << lsu_name << std::endl;
            return STS_EOK;
        } else {
            delete lsu;
            setError(STS_ERR_INTERNAL, "Failed to initialize new LSU");
            return STS_ERR_INTERNAL;
        }
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to create LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::deleteLSU(const std::string& lsu_name)
{
    std::cout << "VastStorageServer: Deleting LSU (S3 bucket): " << lsu_name << std::endl;

    if (!m_connected || !m_rest_client || !m_s3_client) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Find LSU in cache
        auto it = m_lsu_cache.find(lsu_name);
        if (it == m_lsu_cache.end()) {
            setError(STS_ERR_INVALID_PARAMETER, "LSU not found");
            return STS_ERR_INVALID_PARAMETER;
        }

        // Delete the S3 bucket from Vast Data
        std::string bucket_name = lsu_name;
        int result = m_s3_client->deleteBucket(bucket_name);
        if (result != 0) {
            std::cout << "VastStorageServer: Warning - Failed to delete S3 bucket: " << bucket_name
                      << " (may not exist or may contain objects)" << std::endl;
            // Continue with LSU cleanup even if bucket deletion fails
        } else {
            std::cout << "VastStorageServer: Successfully deleted S3 bucket: " << bucket_name << std::endl;
        }

        // Remove from cache
        delete it->second;
        m_lsu_cache.erase(it);
        std::cout << "VastStorageServer: Successfully deleted LSU: " << lsu_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to delete LSU: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::refreshAuthToken()
{
    std::cout << "VastStorageServer: Refreshing authentication token" << std::endl;

    if (!m_rest_client) {
        setError(STS_ERR_NOT_CONNECTED, "REST client not initialized");
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // Try to refresh using the refresh token first
        if (!m_refresh_token.empty()) {
            VastAuthTokens tokens;
            int result = m_rest_client->refreshToken(m_refresh_token, tokens);
            if (result == 0) {
                // Successfully refreshed
                m_auth_token = tokens.access_token;
                m_refresh_token = tokens.refresh_token;
                std::cout << "VastStorageServer: Successfully refreshed authentication token" << std::endl;
                return STS_EOK;
            }
            std::cout << "VastStorageServer: Token refresh failed, re-authenticating..." << std::endl;
        }

        // Fall back to full re-authentication
        return authenticateToVms();
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Token refresh failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::getServerConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // Return basic server configuration as JSON
    std::ostringstream config;
    config << "{\n";
    config << "  \"server_name\": \"" << m_server_name << "\",\n";
    config << "  \"vms_endpoint\": \"" << m_vms_endpoint << "\",\n";
    config << "  \"s3_endpoint\": \"" << m_s3_endpoint << "\",\n";
    config << "  \"connected\": " << (m_connected ? "true" : "false") << "\n";
    config << "}";

    std::string config_str = config.str();
    *maxlen = static_cast<sts_uint32_t>(config_str.length());

    if (buflen < *maxlen + 1) {
        return STS_ERR_BUFFER_TOO_SMALL;
    }

    strncpy(buf, config_str.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';

    return STS_EOK;
}

int VastStorageServer::ensureInfrastructure() {
    std::cout << "VastStorageServer: Ensuring required infrastructure exists" << std::endl;

    try {
        // Step 1: Ensure default tenant exists
        int result = ensureTenant("netbackup");
        if (result != STS_EOK) {
            std::cerr << "VastStorageServer: Failed to ensure tenant" << std::endl;
            return result;
        }

        // Step 2: Ensure default S3 view exists
        result = ensureView("netbackup-s3", "netbackup");
        if (result != STS_EOK) {
            std::cerr << "VastStorageServer: Failed to ensure view" << std::endl;
            return result;
        }

        // Step 3: Ensure default buckets exist
        result = ensureDefaultBuckets();
        if (result != STS_EOK) {
            std::cerr << "VastStorageServer: Failed to ensure buckets" << std::endl;
            return result;
        }

        std::cout << "VastStorageServer: Infrastructure setup complete" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "VastStorageServer: Infrastructure setup failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::ensureTenant(const std::string& tenant_name) {
    std::cout << "VastStorageServer: Ensuring tenant exists: " << tenant_name << std::endl;

    try {
        // Check if tenant already exists
        std::vector<VastTenantInfo> tenants;
        int result = m_rest_client->listTenants(tenants);
        if (result == STS_EOK) {
            for (const auto& tenant : tenants) {
                if (tenant.name == tenant_name) {
                    std::cout << "VastStorageServer: Tenant already exists: " << tenant_name << std::endl;
                    return STS_EOK;
                }
            }
        }

        // Create tenant if it doesn't exist
        VastTenantInfo tenant_info;
        tenant_info.name = tenant_name;
        tenant_info.description = "NetBackup Storage Tenant";
        // Use properties map instead of direct fields
        tenant_info.properties["ssd_enabled"] = "true";
        tenant_info.properties["trash_enabled"] = "true";

        result = m_rest_client->createTenant(tenant_info);
        if (result == STS_EOK) {
            std::cout << "VastStorageServer: Successfully created tenant: " << tenant_name << std::endl;
        } else {
            std::cerr << "VastStorageServer: Failed to create tenant: " << tenant_name << std::endl;
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "VastStorageServer: Tenant creation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::ensureView(const std::string& view_name, const std::string& tenant_name) {
    std::cout << "VastStorageServer: Ensuring view exists: " << view_name << " in tenant: " << tenant_name << std::endl;

    try {
        // Check if view already exists
        std::vector<VastViewInfo> views;
        int result = m_rest_client->listViews(views);
        if (result == STS_EOK) {
            for (const auto& view : views) {
                // Check properties map for tenant_name instead of direct field
                if (view.name == view_name && 
                    view.properties.find("tenant") != view.properties.end() && 
                    view.properties.at("tenant") == tenant_name) {
                    std::cout << "VastStorageServer: View already exists: " << view_name << std::endl;
                    return STS_EOK;
                }
            }
        }

        // Create view if it doesn't exist
        VastViewInfo view_info;
        view_info.name = view_name;
        view_info.path = "/" + view_name;
        // Use properties map instead of direct fields
        view_info.properties["tenant"] = tenant_name;
        view_info.properties["nfs_enabled"] = "false";
        view_info.properties["smb_enabled"] = "false";
        view_info.properties["s3_enabled"] = "true";
        view_info.protocol = "s3";  // Use the available protocol field

        result = m_rest_client->createView(view_info);
        if (result == STS_EOK) {
            std::cout << "VastStorageServer: Successfully created view: " << view_name << std::endl;
        } else {
            std::cerr << "VastStorageServer: Failed to create view: " << view_name << std::endl;
        }

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "VastStorageServer: View creation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::ensureDefaultBuckets() {
    std::cout << "VastStorageServer: Ensuring default buckets exist" << std::endl;

    try {
        // List existing buckets
        std::vector<std::string> existing_buckets;
        int result = m_s3_client->listBuckets(existing_buckets);

        // If no buckets exist, create a default one
        if (result == STS_EOK && existing_buckets.empty()) {
            std::string default_bucket = "netbackup-pool-01";
            std::cout << "VastStorageServer: No buckets found, creating default: " << default_bucket << std::endl;

            result = m_s3_client->createBucket(default_bucket);
            if (result == STS_EOK) {
                std::cout << "VastStorageServer: Successfully created default bucket: " << default_bucket << std::endl;
            } else {
                std::cerr << "VastStorageServer: Failed to create default bucket: " << default_bucket << std::endl;
                return result;
            }
        } else if (result == STS_EOK) {
            std::cout << "VastStorageServer: Found " << existing_buckets.size() << " existing buckets" << std::endl;
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "VastStorageServer: Default bucket creation failed: " << e.what() << std::endl;
        return STS_ERR_INTERNAL;
    }
}

int VastStorageServer::setServerConfig(const char* buf, char* msgbuf, sts_uint32_t msgbuflen)
{
    if (!buf) {
        return STS_ERR_INVALID_PARAMETER;
    }

    // For now, return not supported - configuration should be done through VMS
    if (msgbuf && msgbuflen > 0) {
        const char* msg = "Server configuration changes not supported through this interface";
        strncpy(msgbuf, msg, msgbuflen - 1);
        msgbuf[msgbuflen - 1] = '\0';
    }

    return STS_ERR_NOT_SUPPORTED;
}

int VastStorageServer::getImageList(const std::string& lsu_name, std::vector<VastImage>& image_list, int type)
{
    std::cout << "VastStorageServer: Getting image list for LSU: " << lsu_name << std::endl;
    
    clearError();
    image_list.clear();
    
    if (!m_connected) {
        setError(STS_ERR_NOT_CONNECTED, "Not connected to server");
        return STS_ERR_NOT_CONNECTED;
    }
    
    try {
        // Get the LSU object
        VastLSU* lsu = getLSU(lsu_name);
        if (!lsu) {
            setError(STS_ERR_INVALID_PARAMETER, "LSU not found: " + lsu_name);
            return STS_ERR_INVALID_PARAMETER;
        }
        
        // Call the LSU's actual getImageList implementation
        int result = lsu->getImageList(image_list);
        if (result != STS_EOK) {
            setError(result, "Failed to get image list from LSU");
            return result;
        }
        
        // Filter by type if needed
        if (type != 0) {
            // Type filtering logic would go here
            // For now, return all images regardless of type
            std::cout << "VastStorageServer: Type filtering not yet implemented, returning all images" << std::endl;
        }
        
        std::cout << "VastStorageServer: Successfully retrieved " << image_list.size() 
                  << " images for LSU " << lsu_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Failed to get image list: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

// Private methods implementation

int VastStorageServer::authenticateToVms()
{
    std::cout << "VastStorageServer: Verifying cluster connectivity and authenticating to VMS" << std::endl;

    if (!m_rest_client) {
        return STS_ERR_NOT_CONNECTED;
    }

    try {
        // First verify cluster is operational (before authentication)
        std::map<std::string, std::string> cluster_status;
        int result = m_rest_client->getClusterStatus(cluster_status);
        if (result != STS_EOK) {
            setError(STS_ERR_CONNECT, "Cannot connect to Vast Data cluster at " + m_vms_endpoint +
                     ". Ensure cluster is deployed, VMS is running, and network connectivity exists.");
            return STS_ERR_CONNECT;
        }

        std::cout << "VastStorageServer: Cluster connectivity verified" << std::endl;

        // Authenticate using actual Vast Data REST API
        VastAuthTokens tokens;
        result = m_rest_client->authenticate(m_username, m_password, tokens);
        if (result != 0) {
            setError(STS_ERR_AUTH_FAILED, "VMS authentication failed. Check credentials.");
            return STS_ERR_AUTH_FAILED;
        }

        // Store authentication tokens
        m_auth_token = tokens.access_token;
        m_refresh_token = tokens.refresh_token;

        std::cout << "VastStorageServer: Successfully authenticated to VMS" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_AUTH_FAILED, std::string("Authentication failed: ") + e.what());
        return STS_ERR_AUTH_FAILED;
    }
}

int VastStorageServer::initializeS3Client()
{
    std::cout << "VastStorageServer: Initializing S3 client" << std::endl;

    try {
        m_s3_client = std::make_unique<VastS3Client>();

        // Create S3 credentials for NetBackup operations
        VastS3KeyPair s3_credentials;
        std::string key_name = "netbackup-" + m_server_name;
        int result = m_rest_client->createS3Key(key_name, m_username, s3_credentials);
        if (result != 0) {
            // Try to get existing key if creation failed
            std::vector<VastS3KeyPair> existing_keys;
            result = m_rest_client->getS3Keys(existing_keys);
            if (result == 0 && !existing_keys.empty()) {
                // Use the first available key
                s3_credentials = existing_keys[0];
                std::cout << "VastStorageServer: Using existing S3 key: " << s3_credentials.access_key << std::endl;
            } else {
                setError(result, "Failed to create or get S3 credentials");
                return result;
            }
        } else {
            std::cout << "VastStorageServer: Created new S3 key: " << s3_credentials.access_key << std::endl;
        }

        // Store S3 credentials
        m_access_key = s3_credentials.access_key;
        m_secret_key = s3_credentials.secret_key;

        // Initialize S3 client with proper credentials
        result = m_s3_client->initialize(m_s3_endpoint, s3_credentials.access_key, s3_credentials.secret_key);
        if (result != STS_EOK) {
            setError(result, "Failed to initialize S3 client with credentials");
            return result;
        }

        std::cout << "VastStorageServer: Successfully initialized S3 client" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("S3 client initialization failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

void VastStorageServer::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastStorageServer Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastStorageServer::clearError()
{
    m_last_error = STS_EOK;
    m_last_error_msg.clear();
}