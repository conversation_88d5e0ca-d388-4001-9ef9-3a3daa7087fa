/*
 *************************************************************************
 * Vast Data OpenStorage Plugin Implementation
 * Main implementation of OST API functions for Vast Data
 *************************************************************************
 */

#include "vastplugin.h"
#include "VastStorageServer.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include <iostream>
#include <cstring>
#include <map>
#include <fstream>

// Global variables
static bool g_plugin_initialized = false;
static std::map<std::string, VastStorageServer*> g_server_cache;

/*********************************************************************************
 * Core Plugin Initialization Functions
 *********************************************************************************/

STS_EXPORT int stspi_init(
    sts_uint64_t masterVersion,
    const char* path,
    stspi_api_t* stspAPI)
{
    std::cout << "Vast Data OST Plugin: Initializing plugin" << std::endl;
    
    if (!stspAPI) {
        return STS_EINVAL;
    }
    
    if (g_plugin_initialized) {
        // Still need to populate API table on every call
        // Fall through to populate stspAPI
    }

    // Verify API version compatibility
    if (masterVersion < STS_MINIMUM_VERSION) {
        std::cerr << "Vast Data OST Plugin: Incompatible API version" << std::endl;
        return STS_EPLUGINVERSION;
    }

    // Initialize plugin resources
    try {
        if (!g_plugin_initialized) {
            // Initialize any global resources here
            g_plugin_initialized = true;
        }

        // Populate API table based on master version
        memset(stspAPI, 0, sizeof(stspi_api_t));
        
        // Set version to the lesser of what we support and what was requested
        sts_uint64_t our_max_version = 11; // We support version 11
        sts_uint64_t operating_version = (masterVersion < our_max_version) ? masterVersion : our_max_version;
        
        stspAPI->spx_version = operating_version;
        
        // Populate plugin definition
        stspAPI->spx_def.spd_npfx = 1;
        // Set appropriate capability flags for Vast Data plugin
        stspAPI->spx_def.spd_flags = STS_SPD_LOCALONLY | STS_SPD_ASYNC | STS_SPD_COPY;
        stspAPI->spx_def.spd_build_version = 11;
        stspAPI->spx_def.spd_build_version_minor = 1;
        stspAPI->spx_def.spd_operating_version = operating_version;
        
        // Set plugin prefix
        strncpy(stspAPI->spx_def.spd_pfxdef[0].spp_pfx, "vast:", sizeof(stspAPI->spx_def.spd_pfxdef[0].spp_pfx) - 1);
        strncpy(stspAPI->spx_def.spd_pfxdef[0].spp_label, "Vast Data Storage", sizeof(stspAPI->spx_def.spd_pfxdef[0].spp_label) - 1);
        
        // Set vendor version
        strncpy(stspAPI->spx_def.spd_vendor_version, "Vast Data OST Plugin v1.0.0", sizeof(stspAPI->spx_def.spd_vendor_version) - 1);
        
        // We only support version 11 for now
        if (operating_version != 11) {
            std::cerr << "Vast Data OST Plugin: Only version 11 is supported" << std::endl;
            return STS_EPLUGINVERSION;
        }
        
        // Need to create and set a static entry points structure
        static stspi_ep_v11_t VastPluginEntryPoints_v11 = {
            stspi_flush_image,                     // v11_flush
            stspi_async_read_image_v11,            // v11_async_read_image
            stspi_async_wait_v11,                  // v11_async_wait
            stspi_async_write_image_v11,           // v11_async_write_image
            stspi_claim,                           // v11_claim
            stspi_close_evchannel_v9,              // v11_close_evchannel
            stspi_close_image,                     // v11_close_image
            stspi_close_image_list,                // v11_close_image_list
            stspi_close_lsu_list,                  // v11_close_lsu_list
            stspi_close_server,                    // v11_close_server
            NULL,                                  // v11_copy_extent
            stspi_create_image_v10,                // v11_create_image
            NULL,                                  // v11_delete_event
            NULL,                                  // v11_delete_files
            stspi_delete_image_v10,                // v11_delete_image
            stspi_get_event_v11,                   // v11_get_event
            NULL,                                  // v11_get_image_group
            NULL,                                  // v11_get_image_group_byname
            stspi_get_image_prop_v10,              // v11_get_image_prop
            stspi_get_image_prop_byname_v10,       // v11_get_image_prop_byname
            stspi_get_lsu_prop_byname_v11,         // v11_get_lsu_prop_byname
            stspi_get_server_prop,                 // v11_get_server_prop
            stspi_get_server_prop_byname,          // v11_get_server_prop_byname
            NULL,                                  // v11_include_in_image
            NULL,                                  // v11_ioctl
            stspi_list_image_v10,                  // v11_list_image
            stspi_list_lsu,                        // v11_list_lsu
            stspi_open_evchannel_v11,              // v11_open_evchannel
            stspi_open_lsu_list_v11,               // v11_open_lsu_list
            stspi_open_server,                     // v11_open_server
            NULL,                                  // v11_open_target_server
            stspi_open_image_v10,                  // v11_open_image
            NULL,                                  // v11_open_image_group_list
            stspi_open_image_list_v10,             // v11_open_image_list
            stspi_read_image,                      // v11_read_image
            stspi_terminate,                       // v11_terminate
            stspi_write_image,                     // v11_write_image
            NULL,                                  // v11_find_lsu
            stspi_label_lsu,                       // v11_label_lsu
            stspi_read_image_meta,                 // v11_read_image_meta
            stspi_write_image_meta,                // v11_write_image_meta
            NULL,                                  // v11_async_cancel
            stspi_async_copy_image_v11,            // v11_async_copy_image
            stspi_copy_image_v11,                  // v11_copy_image
            NULL,                                  // v11_get_event_payload
            NULL,                                  // v11_named_async_cancel
            NULL,                                  // v11_named_async_copy_image
            NULL,                                  // v11_named_async_status
            NULL,                                  // v11_named_async_wait
            NULL,                                  // v11_get_server_config
            NULL,                                  // v11_set_server_config
            NULL,                                  // v11_begin_copy_image
            NULL,                                  // v11_end_copy_image
            NULL,                                  // v11_async_end_copy_image
            NULL,                                  // v11_named_async_end_copy_image
            NULL,                                  // v11_get_lsu_replication_prop
            NULL                                   // v11_iocontrol
        };
        
        // Set the entry points structure pointer
        stspAPI->spx_ep.v11_ep = &VastPluginEntryPoints_v11;
        
        std::cout << "Vast Data OST Plugin: Successfully initialized with version " << operating_version << std::endl;
        return STS_EOK;  // Use STS_EOK (0) instead of STS_SUCCESS
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Initialization failed: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_claim(const sts_server_name_v7_t serverName)
{
    std::cout << "Vast Data OST Plugin: Claiming server: " << serverName << std::endl;

    // Check if this server name matches our plugin pattern
    // For Vast Data, we expect server names like "vast:vms-endpoint" or "vast://vms-endpoint"
    std::string server_str(serverName);
    if (server_str.find("vast:") == 0) {
        return STS_EOK;
    }

    return STS_ECLAIM;
}

STS_EXPORT int stspi_terminate()
{
    std::cout << "Vast Data OST Plugin: Terminating plugin" << std::endl;

    // Cleanup all cached servers
    for (auto& pair : g_server_cache) {
        delete pair.second;
    }
    g_server_cache.clear();

    g_plugin_initialized = false;
    return STS_EOK;
}

/*********************************************************************************
 * Server Management Functions
 *********************************************************************************/

STS_EXPORT int stspi_get_server_prop_byname(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t serverName,
    sts_server_info_v8_t* serverInfo)
{
    std::cout << "Vast Data OST Plugin: Getting server properties for: " << serverName << std::endl;
    
    if (!serverInfo) {
        return STS_EINVAL;
    }

    // Parse server name to extract VMS endpoint
    std::string server_str(serverName);
    if (server_str.find("vast:") != 0) {
        return STS_ECLAIM;
    }

    // Fill in basic server information
    strncpy(serverInfo->srv_server, serverName, sizeof(serverInfo->srv_server) - 1);
    serverInfo->srv_server[sizeof(serverInfo->srv_server) - 1] = '\0';

    // Set server capabilities
    serverInfo->srv_flags = STS_SRV_IMAGELIST | STS_SRV_CRED | STS_SRV_CONRW | STS_SRV_IMAGE_COPY;
    
    // Set server capabilities using proper flags
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLAIM);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_LSU_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_SERVER);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CREATE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_LSU);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_LSU_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_SERVER);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_READ_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_TERMINATE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_WRITE_IMAGE);
    
    serverInfo->srv_maxconnect = 10;
    serverInfo->srv_nconnect = 0;
    
    return STS_EOK;
}

STS_EXPORT int stspi_open_server(
    const sts_session_def_v7_t* session,
    const sts_server_name_v7_t sts_server_name,
    const sts_cred_v7_t* credentials,
    const sts_interface_v7_t stsInterface,
    stsp_server_handle_t* sh)
{
    std::cout << "Vast Data OST Plugin: Opening server connection to: " << sts_server_name << std::endl;
    
    if (!sh || !credentials) {
        return STS_EINVAL;
    }

    try {
        // Parse server name to extract endpoints
        std::string server_str(sts_server_name);
        if (server_str.find("vast:") != 0) {
            return STS_ECLAIM;
        }

        // Extract VMS endpoint from server name
        std::string vms_endpoint;
        if (server_str.find("vast://") == 0) {
            vms_endpoint = server_str.substr(7); // Remove "vast://"
        } else {
            vms_endpoint = server_str.substr(5); // Remove "vast:"
        }
        
        // S3 endpoint is typically different from VMS endpoint
        std::string s3_endpoint = "s3." + vms_endpoint;

        // Create or get cached server instance
        auto it = g_server_cache.find(server_str);
        VastStorageServer* vast_server = nullptr;
        
        if (it != g_server_cache.end()) {
            vast_server = it->second;
        } else {
            vast_server = new VastStorageServer();
            g_server_cache[server_str] = vast_server;
        }

        // Extract credentials from cert field (simplified)
        std::string cred_str(credentials->cr_cert);
        std::string username = "admin";  // Default username
        std::string password = cred_str;  // Use cert as password for now

        // Connect to Vast Data
        int result = vast_server->connect(
            sts_server_name,
            username,
            password,
            "https://" + vms_endpoint + "/api",
            "https://" + s3_endpoint
        );

        if (result != STS_EOK) {
            std::cerr << "Failed to connect to Vast Data server: " << vast_server->getLastErrorMessage() << std::endl;
            return result;
        }

        // Allocate and initialize handle
        *sh = new stsp_server_handle_s;
        (*sh)->vast_server = vast_server;
        (*sh)->session = session;
        strncpy((*sh)->server_name, sts_server_name, sizeof((*sh)->server_name) - 1);
        (*sh)->server_name[sizeof((*sh)->server_name) - 1] = '\0';

        std::cout << "Vast Data OST Plugin: Successfully connected to server" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Server connection failed: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_get_server_prop(
    stsp_server_handle_t sh,
    sts_server_info_v8_t* serverInfo)
{
    std::cout << "Vast Data OST Plugin: Getting server properties" << std::endl;
    
    if (!sh || !sh->vast_server || !serverInfo) {
        return STS_EINVAL;
    }

    // Fill in basic server information
    strncpy(serverInfo->srv_server, sh->server_name, sizeof(serverInfo->srv_server) - 1);
    serverInfo->srv_server[sizeof(serverInfo->srv_server) - 1] = '\0';

    // Set server capabilities
    serverInfo->srv_flags = STS_SRV_IMAGELIST | STS_SRV_CRED | STS_SRV_CONRW | STS_SRV_IMAGE_COPY;
    
    // Set server capabilities using proper flags
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLAIM);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_LSU_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_SERVER);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_CREATE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP_BYNAME);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_LSU);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_LSU_LIST);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_SERVER);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_READ_IMAGE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_TERMINATE);
    STS_SET_SRV_CAP(serverInfo, STS_SRVC_WRITE_IMAGE);
    
    serverInfo->srv_maxconnect = 10;
    serverInfo->srv_nconnect = 0;
    
    return STS_EOK;
}

STS_EXPORT int stspi_close_server(stsp_server_handle_t sh)
{
    std::cout << "Vast Data OST Plugin: Closing server connection" << std::endl;
    
    if (!sh) {
        return STS_EINVAL;
    }

    // Don't actually delete the VastStorageServer instance
    // Just disconnect it - we're keeping it cached in g_server_cache
    if (sh->vast_server) {
        sh->vast_server->disconnect();
    }
    
    // Free the handle
    delete sh;
    
    return STS_EOK;
}

STS_EXPORT int stspi_open_lsu_list_v11(
    stsp_server_handle_t sh,
    const sts_lsu_def_v11_t* lsudef,
    stsp_lsu_list_handle_t* lsu_list_handle)
{
    std::cout << "Vast Data OST Plugin: Opening LSU list" << std::endl;
    
    if (!sh || !sh->vast_server || !lsu_list_handle) {
        return STS_EINVAL;
    }

    try {
        // Create and initialize the LSU list handle
        *lsu_list_handle = new stsp_lsu_list_handle_s;
        (*lsu_list_handle)->server_handle = sh;
        (*lsu_list_handle)->cursor = 0;

        // Get the list of Vast Data views/buckets that will be represented as LSUs
        std::vector<VastLSU> lsu_list;
        int result = sh->vast_server->getLSUList(lsu_list);
        
        if (result != STS_EOK) {
            delete *lsu_list_handle;
            return result;
        }

        // Store the list in the handle
        (*lsu_list_handle)->lsu_list = lsu_list;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to open LSU list: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_list_lsu(
    stsp_lsu_list_handle_t lsuListHandle,
    sts_lsu_name_t* lsuName)
{
    std::cout << "Vast Data OST Plugin: Listing LSUs" << std::endl;
    
    if (!lsuListHandle || !lsuName) {
        return STS_EINVAL;
    }
    
    try {
        // Check if we've reached the end of the LSU list
        if (lsuListHandle->cursor >= lsuListHandle->lsu_list.size()) {
            return STS_ENOENT;  // Use standard SDK error code for "no more entries"
        }
        
        // Get the current LSU and copy its name
        const VastLSU& lsu = lsuListHandle->lsu_list[lsuListHandle->cursor];
        strncpy(lsuName->sln_name, lsu.getName().c_str(), STS_MAX_LSUNAME - 1);
        lsuName->sln_name[STS_MAX_LSUNAME - 1] = '\0';
        
        // Increment the cursor
        lsuListHandle->cursor++;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to list LSU: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_close_lsu_list(const stsp_lsu_list_handle_t lsuListHandle)
{
    std::cout << "Vast Data OST Plugin: Closing LSU list" << std::endl;
    
    if (!lsuListHandle) {
        return STS_EINVAL;
    }
    
    try {
        // Clear the LSU list and free the handle
        lsuListHandle->lsu_list.clear();
        delete lsuListHandle;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to close LSU list: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_get_lsu_prop_byname_v11(
    const stsp_lsu_t* lsu,
    sts_lsu_info_v11_t* lsuInfo)
{
    std::cout << "Vast Data OST Plugin: Getting LSU properties" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || !lsuInfo) {
        return STS_EINVAL;
    }

    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        
        // Find the requested LSU
        VastLSU vast_lsu(lsu_name, sh->vast_server);
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Populate the LSU info structure
        memset(lsuInfo, 0, sizeof(sts_lsu_info_v11_t));
        
        // Set LSU capacity information
        lsuInfo->lsu_capacity = vast_lsu.getCapacity();
        lsuInfo->lsu_used = vast_lsu.getUsedSpace();
        // Use proper member instead of lsu_free
        sts_uint64_t available_space = vast_lsu.getCapacity() - vast_lsu.getUsedSpace();
        // Since v11 might not have lsu_free, we're using what's available in the struct
        lsuInfo->lsu_used_phys = vast_lsu.getPhysicalSpace();
        lsuInfo->lsu_images = vast_lsu.getImageCount();
        
        // Set LSU definition information
        lsuInfo->lsu_def.version = 11;  // Must match the version requested
        lsuInfo->lsu_def.sld_alloc = STS_LSU_AT_STATIC;  // Static allocation
        lsuInfo->lsu_def.sld_storage = STS_LSU_ST_FILE;  // File-based storage
        
        // Copy LSU name
        strncpy(lsuInfo->lsu_def.sld_name.sln_name, 
                lsu_name.c_str(), 
                sizeof(lsuInfo->lsu_def.sld_name.sln_name) - 1);
        lsuInfo->lsu_def.sld_name.sln_name[sizeof(lsuInfo->lsu_def.sld_name.sln_name) - 1] = '\0';
        
        // Set transfer and block size information
        lsuInfo->lsu_def.sld_max_transfer = 64 * 1024 * 1024;  // 64 MB max transfer size
        lsuInfo->lsu_def.sld_block_size = STS_BLOCK_SIZE;      // Standard block size (512 bytes)
        lsuInfo->lsu_def.sld_flags = 0;                        // No special flags
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to get LSU properties: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_label_lsu(const stsp_lsu_t* lsu, sts_lsu_label_t lsu_label)
{
    std::cout << "Vast Data OST Plugin: Setting LSU label" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || !lsu_label) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        
        // Find the requested LSU
        VastLSU vast_lsu;
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Set the label on the LSU
        result = sh->vast_server->setLSULabel(lsu_name, lsu_label);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to set LSU label: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_create_image_v10(
    const stsp_lsu_t* lsu, 
    const sts_image_def_v10_t* imageDefinition, 
    int pendingFlag, 
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Creating image" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || 
        !imageDefinition || !imageHandle) {
        return STS_EINVAL;
    }

    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        
        // Find the requested LSU
        VastLSU vast_lsu(lsu_name, sh->vast_server);
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Check for existing image with the same name
        std::string image_name(imageDefinition->img_basename);
        VastImage existing_image;
        result = sh->vast_server->getImageByName(lsu_name, image_name, existing_image);
        
        if (result == STS_EOK) {
            // Image already exists
            return STS_EEXIST;
        }
        
        // Create the image
        VastImage* new_image = new VastImage();
        new_image->setName(image_name);
        new_image->setBasename(imageDefinition->img_basename);
        
        // Convert date string to timestamp if needed
        sts_uint64_t timestamp = 0;
        if (imageDefinition->img_date[0] != '\0') {
            timestamp = static_cast<sts_uint64_t>(time(nullptr)); // Default to current time
            // In a real implementation, we would parse the date string here
        }
        
        new_image->setTimestamp(timestamp);
        new_image->setLSUName(lsu_name);
        
        // Check if this is a NetBackup image by examining the DPAID
        if (strcmp(imageDefinition->img_isid.is_dpaid, "NBU_65") == 0) {
            // This is a NetBackup image with specific information
            // Extract NetBackup-specific information
            stsnbu_isinfo_v65_t* nbu_info = (stsnbu_isinfo_v65_t*)imageDefinition->img_isid.is_info;
            
            if (nbu_info) {
                // Set NBU-specific metadata on the image
                new_image->setMasterServer(nbu_info->isi_bckpid.bi_master_server);
                new_image->setBackupTime(nbu_info->isi_bckpid.bi_time);
                new_image->setCopyNumber(nbu_info->isi_bckpid.bi_copy_number);
                new_image->setStreamNumber(nbu_info->isi_strm_num);
                new_image->setFragmentNumber(nbu_info->isi_frag_num);
            }
        }

        // Create the image in Vast
        result = sh->vast_server->createImage(lsu_name, *new_image, pendingFlag);
        
        if (result != STS_EOK) {
            delete new_image;
            return result;
        }
        
        // Create and initialize image handle
        *imageHandle = new stsp_image_handle_s;
        (*imageHandle)->server_handle = sh;
        (*imageHandle)->lsu = *lsu;
        (*imageHandle)->image_def = *imageDefinition;
        (*imageHandle)->vast_image = new_image;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to create image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_open_image_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    int mode,
    stsp_image_handle_t* imageHandle)
{
    std::cout << "Vast Data OST Plugin: Opening image" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || 
        !imageDefinition || !imageHandle) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        std::string image_name(imageDefinition->img_basename);
        
        // Find the requested LSU
        VastLSU vast_lsu;
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Find the requested image
        VastImage* vast_image = new VastImage();
        result = sh->vast_server->getImageByName(lsu_name, image_name, *vast_image);
        
        if (result != STS_EOK) {
            delete vast_image;
            return STS_ENOENT;
        }
        
        // Open the image with the requested mode
        result = sh->vast_server->openImage(lsu_name, *vast_image, mode);
        
        if (result != STS_EOK) {
            delete vast_image;
            return result;
        }
        
        // Create and initialize image handle
        *imageHandle = new stsp_image_handle_s;
        (*imageHandle)->server_handle = sh;
        (*imageHandle)->lsu = *lsu;
        (*imageHandle)->image_def = *imageDefinition;
        (*imageHandle)->vast_image = vast_image;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to open image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_close_image(
    stsp_image_handle_t image_handle,
    int completeFlag, 
    int forceFlag)
{
    std::cout << "Vast Data OST Plugin: Closing image" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        std::string image_name(image_handle->vast_image->getName());
        
        // Complete the image if requested
        if (completeFlag) {
            int result = sh->vast_server->completeImage(lsu_name, *image_handle->vast_image);
            if (result != STS_EOK && !forceFlag) {
                return result;
            }
        }
        
        // Close the image
        int result = sh->vast_server->closeImage(lsu_name, *image_handle->vast_image);
        
        // Clean up resources
        delete image_handle->vast_image;
        delete image_handle;
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to close image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_delete_image_v10(
    const stsp_lsu_t* lsu, 
    const sts_image_def_v10_t* imageDefinition, 
    int asyncFlag)
{
    std::cout << "Vast Data OST Plugin: Deleting image" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || !imageDefinition) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        std::string image_name(imageDefinition->img_basename);
        
        // Find the requested LSU
        VastLSU vast_lsu;
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Delete the image
        // If asyncFlag is set, initiate asynchronous deletion
        if (asyncFlag) {
            // Start asynchronous deletion - in a real plugin, this would launch a background task
            result = sh->vast_server->startAsyncImageDelete(lsu_name, image_name);
        } else {
            // Perform synchronous deletion
            result = sh->vast_server->deleteImage(lsu_name, image_name);
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to delete image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_read_image(
    stsp_image_handle_t image_handle, 
    void* buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t* bytesRead)
{
    std::cout << "Vast Data OST Plugin: Reading image data" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image || 
        !buf || !bytesRead) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Read data from the image
        int result = sh->vast_server->readImageData(
            lsu_name, 
            *image_handle->vast_image, 
            buf, 
            length, 
            offset, 
            bytesRead);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to read image data: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_write_image(
    stsp_image_handle_t image_handle, 
    sts_stat_t* stat, 
    void* buf, 
    sts_uint64_t length, 
    sts_uint64_t offset, 
    sts_uint64_t* bytesWritten)
{
    std::cout << "Vast Data OST Plugin: Writing image data" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image || 
        !buf || !bytesWritten) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Write data to the image
        int result = sh->vast_server->writeImageData(
            lsu_name, 
            *image_handle->vast_image, 
            buf,
            length, 
            offset, 
            bytesWritten);
        
        // Update image size if stat is provided
        if (stat) {
            // Use a field that actually exists in the struct (st_size may not exist)
            sts_uint64_t file_size = length + offset;  // Calculate size from write operation
            image_handle->vast_image->setSize(file_size);
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to write image data: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_read_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* bytesread)
{
    std::cout << "Vast Data OST Plugin: Reading image metadata" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image ||
        !buf || !bytesread) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Read metadata from the image
        int result = sh->vast_server->readImageMetadata(
            lsu_name,
            *image_handle->vast_image,
            buf,
            len,
            offset,
            bytesread);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to read image metadata: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_write_image_meta(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    sts_uint64_t* byteswritten)
{
    std::cout << "Vast Data OST Plugin: Writing image metadata" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image ||
        !buf || !byteswritten) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Write metadata to the image
        int result = sh->vast_server->writeImageMetadata(
            lsu_name,
            *image_handle->vast_image,
            buf,
            len,
            offset,
            byteswritten);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to write image metadata: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_open_image_list_v10(
    const stsp_lsu_t* lsu,
    int type,
    stsp_image_list_handle_t* image_list_handle)
{
    std::cout << "Vast Data OST Plugin: Opening image list" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || !image_list_handle) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        
        // Find the requested LSU
        VastLSU vast_lsu;
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Create and initialize the image list handle
        *image_list_handle = new stsp_image_list_handle_s;
        (*image_list_handle)->server_handle = sh;
        (*image_list_handle)->lsu = *lsu;
        (*image_list_handle)->cursor = 0;
        (*image_list_handle)->type = type;
        
        // Get the list of images for this LSU
        std::vector<VastImage> image_list;
        result = sh->vast_server->getImageList(lsu_name, image_list, type);
        
        if (result != STS_EOK) {
            delete *image_list_handle;
            return result;
        }
        
        // Store the image list in the handle
        (*image_list_handle)->image_list = image_list;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to open image list: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_list_image_v10(
    stsp_image_list_handle_t image_list_handle,
    sts_image_def_v10_t* img)
{
    std::cout << "Vast Data OST Plugin: Listing images" << std::endl;
    
    if (!image_list_handle || !image_list_handle->server_handle || !img) {
        return STS_EINVAL;
    }
    
    try {
        // Check if we've reached the end of the image list
        if (image_list_handle->cursor >= image_list_handle->image_list.size()) {
            return STS_ENOENT;  // Use standard SDK error code for "no more entries"
        }
        
        // Get the current image
        const VastImage& image = image_list_handle->image_list[image_list_handle->cursor];
        
        // Clear the image definition structure
        memset(img, 0, sizeof(sts_image_def_v10_t));
        
        // Copy image information into the definition
        strncpy(img->img_basename, image.getBasename().c_str(), sizeof(img->img_basename) - 1);
        img->img_basename[sizeof(img->img_basename) - 1] = '\0';
        
        // Convert numeric timestamp to string format
        sts_uint64_t timestamp = image.getTimestamp();
        snprintf(img->img_date, sizeof(img->img_date), "%lu", timestamp);
        
        // Increment the cursor
        image_list_handle->cursor++;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to list image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_close_image_list(stsp_image_list_handle_t image_list_handle)
{
    std::cout << "Vast Data OST Plugin: Closing image list" << std::endl;
    
    if (!image_list_handle) {
        return STS_EINVAL;
    }
    
    try {
        // Clear the image list and free the handle
        image_list_handle->image_list.clear();
        delete image_list_handle;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to close image list: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_flush_image(stsp_image_handle_t image_handle)
{
    std::cout << "Vast Data OST Plugin: Flushing image data" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Flush data to the image
        int result = sh->vast_server->flushImage(lsu_name, *image_handle->vast_image);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to flush image: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

/*********************************************************************************
 * Event Channel Functions
 *********************************************************************************/

STS_EXPORT int stspi_close_evchannel_v9(
    stsp_evc_handle_t evc_handle)
{
    std::cout << "Vast Data OST Plugin: Closing event channel" << std::endl;
    
    if (!evc_handle) {
        return STS_EINVAL;
    }
    
    try {
        // Clean up event channel resources
        delete evc_handle;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to close event channel: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_get_event_v11(
    stsp_evc_handle_t evc_handle,
    sts_event_v11_t* event)
{
    std::cout << "Vast Data OST Plugin: Getting event" << std::endl;
    
    if (!evc_handle || !event) {
        return STS_EINVAL;
    }
    
    try {
        // For now, we'll just return no events
        // In a real implementation, this would check for events from Vast Data
        return STS_ENOEVENT;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to get event: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_open_evchannel_v11(
    const sts_session_def_v7_t* sd,
    const sts_server_name_v7_t server,
    const sts_cred_v7_t* cred,
    const sts_interface_v7_t iface,
    sts_evhandler_v11_t handler,
    sts_event_v11_t* event,
    int flags,
    sts_evseqno_v11_t evseqno,
    stsp_evc_handle_t* pevc_handle)
{
    std::cout << "Vast Data OST Plugin: Opening event channel" << std::endl;
    
    if (!sd || !server || !pevc_handle) {
        return STS_EINVAL;
    }

    try {
        // Check if server is valid for our plugin
        std::string server_str(server);
        if (server_str.find("vast:") != 0) {
            return STS_ECLAIM;
        }

        // Determine if this is a push mode (handler provided) or pull mode channel
        bool isPushMode = (handler != NULL);
        
        // For push mode, event must be provided
        if (isPushMode && !event) {
            return STS_EINVAL;
        }
        
        // Create the event channel handle
        *pevc_handle = new stsp_evc_handle_s;
        (*pevc_handle)->server_handle = nullptr; // Will be set when needed
        (*pevc_handle)->flags = flags;
        (*pevc_handle)->mode = isPushMode ? 1 : 0;
        (*pevc_handle)->sequence = evseqno;
        (*pevc_handle)->handler = handler;
        (*pevc_handle)->event = event;

        // In a real implementation, this would setup WebSocket or polling connection
        // to receive real-time events from Vast Data storage
        std::cout << "Vast Data OST Plugin: Event channel successfully opened" << std::endl;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to open event channel: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

/*********************************************************************************
 * Asynchronous I/O Functions
 *********************************************************************************/

STS_EXPORT int stspi_async_read_image_v11(
    stsp_image_handle_t image_handle,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    stsp_opid_t* opid)
{
    std::cout << "Vast Data OST Plugin: Starting async read operation" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image || 
        !buf || !opid) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Start asynchronous read operation
        int result = sh->vast_server->startAsyncRead(
            lsu_name,
            *image_handle->vast_image,
            buf,
            len,
            offset,
            opid);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to start async read: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_async_write_image_v11(
    stsp_image_handle_t image_handle,
    sts_stat_v7_t* stat,
    void* buf,
    sts_uint64_t len,
    sts_uint64_t offset,
    stsp_opid_t* opid)
{
    std::cout << "Vast Data OST Plugin: Starting async write operation" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image || 
        !buf || !opid) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = image_handle->server_handle;
        std::string lsu_name(image_handle->lsu.sl_lsu_name.sln_name);
        
        // Start asynchronous write operation
        int result = sh->vast_server->startAsyncWrite(
            lsu_name,
            *image_handle->vast_image,
            buf,
            len,
            offset,
            opid);
        
        // Update image size if stat is provided
        if (stat) {
            // Use a field that actually exists in the struct (st_size may not exist)
            sts_uint64_t file_size = len + offset;  // Calculate size from write operation
            image_handle->vast_image->setSize(file_size);
        }
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to start async write: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_async_wait_v11(
    const sts_session_def_v7_t* sd,
    stsp_opid_t opid,
    int blockflag,
    sts_aioresult_v11_t* result)
{
    std::cout << "Vast Data OST Plugin: Waiting for async operation to complete" << std::endl;
    
    if (!sd || !opid || !result) {
        return STS_EINVAL;
    }
    
    try {
        // Wait for the asynchronous operation to complete
        int waitResult = opid->vast_server->waitForAsyncOperation(opid, blockflag, result);
        
        return waitResult;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Async wait failed: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

/*********************************************************************************
 * Image Copy Functions
 *********************************************************************************/

STS_EXPORT int stspi_copy_image_v11(
    const stsp_lsu_v7_t* to_lsu,
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_v7_t* from_lsu,
    const sts_image_def_v10_t* from_img,
    const sts_opname_v11_t imageset,
    int eventflag)
{
    std::cout << "Vast Data OST Plugin: Copying image" << std::endl;
    
    if (!to_lsu || !to_lsu->sl_server_handle || !to_lsu->sl_server_handle->vast_server || 
        !from_lsu || !from_lsu->sl_server_handle || !from_lsu->sl_server_handle->vast_server || 
        !to_img || !from_img) {
        return STS_EINVAL;
    }
    
    try {
        std::string to_lsu_name(to_lsu->sl_lsu_name.sln_name);
        std::string from_lsu_name(from_lsu->sl_lsu_name.sln_name);
        std::string to_img_name(to_img->img_basename);
        std::string from_img_name(from_img->img_basename);
        
        // Both LSUs must be from the same storage server for direct copy
        if (to_lsu->sl_server_handle != from_lsu->sl_server_handle) {
            return STS_EINTERNAL;
        }
        
        stsp_server_handle_t sh = to_lsu->sl_server_handle;
        
        // Perform the copy operation
        int result = sh->vast_server->copyImage(
            from_lsu_name, 
            from_img_name, 
            to_lsu_name, 
            to_img_name,
            imageset.op_name);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Image copy failed: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_async_copy_image_v11(
    const stsp_lsu_v7_t* to_lsu,
    const sts_image_def_v10_t* to_img,
    const stsp_lsu_v7_t* from_lsu,
    const sts_image_def_v10_t* from_img,
    stsp_opid_t* opid,
    const sts_opname_v11_t imageset,
    int eventflag)
{
    std::cout << "Vast Data OST Plugin: Starting async image copy" << std::endl;
    
    if (!to_lsu || !to_lsu->sl_server_handle || !to_lsu->sl_server_handle->vast_server || 
        !from_lsu || !from_lsu->sl_server_handle || !from_lsu->sl_server_handle->vast_server || 
        !to_img || !from_img || !opid) {
        return STS_EINVAL;
    }
    
    try {
        std::string to_lsu_name(to_lsu->sl_lsu_name.sln_name);
        std::string from_lsu_name(from_lsu->sl_lsu_name.sln_name);
        std::string to_img_name(to_img->img_basename);
        std::string from_img_name(from_img->img_basename);
        
        // Both LSUs must be from the same storage server for direct copy
        if (to_lsu->sl_server_handle != from_lsu->sl_server_handle) {
            return STS_EINTERNAL;
        }
        
        stsp_server_handle_t sh = to_lsu->sl_server_handle;
        
        // Start the asynchronous copy operation
        int result = sh->vast_server->startAsyncCopyImage(
            from_lsu_name,
            from_img_name,
            to_lsu_name,
            to_img_name,
            opid,
            imageset.op_name);
        
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Async image copy failed: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

/*********************************************************************************
 * Image Property Functions
 *********************************************************************************/

STS_EXPORT int stspi_get_image_prop_v10(
    stsp_image_handle_t image_handle,
    sts_image_info_v10_t* image_info)
{
    std::cout << "Vast Data OST Plugin: Getting image properties" << std::endl;
    
    if (!image_handle || !image_handle->server_handle || 
        !image_handle->server_handle->vast_server || !image_handle->vast_image || 
        !image_info) {
        return STS_EINVAL;
    }
    
    try {
        // Get image info from the VastImage object
        int result = image_handle->vast_image->getImageInfo(image_info);
        
        if (result != STS_EOK) {
            return result;
        }
        
        // Set server and LSU information
        strncpy(image_info->imo_server, 
                image_handle->server_handle->server_name, 
                sizeof(image_info->imo_server) - 1);
        image_info->imo_server[sizeof(image_info->imo_server) - 1] = '\0';
        
        image_info->imo_lsu = image_handle->lsu.sl_lsu_name;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to get image properties: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}

STS_EXPORT int stspi_get_image_prop_byname_v10(
    const stsp_lsu_t* lsu,
    const sts_image_def_v10_t* imageDefinition,
    sts_image_info_v10_t* imageInfo)
{
    std::cout << "Vast Data OST Plugin: Getting image properties by name" << std::endl;
    
    if (!lsu || !lsu->sl_server_handle || !lsu->sl_server_handle->vast_server || 
        !imageDefinition || !imageInfo) {
        return STS_EINVAL;
    }
    
    try {
        stsp_server_handle_t sh = lsu->sl_server_handle;
        std::string lsu_name(lsu->sl_lsu_name.sln_name);
        std::string image_name(imageDefinition->img_basename);
        
        // Find the requested LSU
        VastLSU vast_lsu;
        int result = sh->vast_server->getLSUByName(lsu_name, vast_lsu);
        
        if (result != STS_EOK) {
            return STS_ENOENT;
        }
        
        // Get image info from the LSU
        result = vast_lsu.getImageInfo(imageDefinition, imageInfo);
        
        if (result != STS_EOK) {
            return result;
        }
        
        // Set server and LSU information
        strncpy(imageInfo->imo_server, sh->server_name, sizeof(imageInfo->imo_server) - 1);
        imageInfo->imo_server[sizeof(imageInfo->imo_server) - 1] = '\0';
        
        imageInfo->imo_lsu = lsu->sl_lsu_name;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        std::cerr << "Vast Data OST Plugin: Failed to get image properties by name: " << e.what() << std::endl;
        return STS_EINTERNAL;
    }
}