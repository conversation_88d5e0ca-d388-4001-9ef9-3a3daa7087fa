/*
 *************************************************************************
 * Vast Data LSU (Logical Storage Unit) Implementation
 * Represents a Vast Data volume/bucket for NetBackup storage
 *************************************************************************
 */

#include "VastLSU.h"
#include "VastStorageServer.h"
#include "VastS3Client.h"
#include "VastRestClient.h"  // Add this include to fix incomplete type issue
#include "VastImage.h"
#include <iostream>
#include <sstream>  // Add this include for std::stringstream
#include <algorithm>  // Add this for std::max
#include <cstring>
#include <memory>

VastLSU::VastLSU()
    : m_total_capacity(0),
      m_used_capacity(0),
      m_available_capacity(0),
      m_image_count(0),
      m_server(nullptr),
      m_last_error(STS_EOK),
      m_max_transfer_size(64 * 1024 * 1024),  // 64MB
      m_block_size(STS_BLOCK_SIZE),
      m_lsu_flags(0)
{
    m_status = "online";  // Default status is online
    initDefaults();
}

VastLSU::VastLSU(const std::string& name, VastStorageServer* server)
    : m_name(name),
      m_total_capacity(0),
      m_used_capacity(0),
      m_available_capacity(0),
      m_image_count(0),
      m_server(server),
      m_last_error(STS_EOK),
      m_max_transfer_size(64 * 1024 * 1024),  // 64MB
      m_block_size(STS_BLOCK_SIZE),
      m_lsu_flags(0)
{
    m_status = "online";  // Default status is online
    m_bucket_name = name;  // Default bucket name same as LSU name
    initDefaults();
}

VastLSU::~VastLSU()
{
    // Clean up image cache
    for (auto* image : m_image_cache) {
        delete image;
    }
    m_image_cache.clear();
}

void VastLSU::initDefaults()
{
    // Set default values
    if (m_bucket_name.empty() && !m_name.empty()) {
        m_bucket_name = m_name;
    }

    if (m_path.empty() && !m_name.empty()) {
        m_path = "/" + m_name;
    }

    // Default capacity values - will be updated by refreshCapacityInfo
    if (m_total_capacity == 0) {
        m_total_capacity = 1024ULL * 1024ULL * 1024ULL * 1024ULL;  // 1 TB
        m_available_capacity = m_total_capacity;
        m_used_capacity = 0;
    }
}

int VastLSU::initialize(const std::string& bucket_name)
{
    std::cout << "VastLSU: Initializing LSU " << m_name << std::endl;
    
    clearError();
    
    if (bucket_name.empty()) {
        m_bucket_name = m_name;  // Use LSU name as bucket name
    } else {
        m_bucket_name = bucket_name;
    }
    
    m_path = "/" + m_bucket_name;

    // Verify bucket exists if we have S3 client access
    VastS3Client* s3_client = getS3Client();
    if (s3_client) {
        int result = s3_client->headBucket(m_bucket_name);
        if (result != 0) {
            std::cout << "VastLSU: Warning - Bucket " << m_bucket_name << " may not exist yet" << std::endl;
        }
    }

    std::cout << "VastLSU: Successfully initialized LSU " << m_name
              << " with bucket " << m_bucket_name << std::endl;
    
    return STS_EOK;
}

int VastLSU::updateInfo()
{
    std::cout << "VastLSU: Updating LSU info for " << m_name << std::endl;
    
    try {
        clearError();
        
        // Get S3 client to check bucket status and calculate usage
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            std::cout << "VastLSU: S3 client not available, skipping capacity update" << std::endl;
            return STS_EOK; // Can't update without S3 client, but not an error
        }

        // Check if bucket exists and is accessible
        int result = s3_client->headBucket(m_bucket_name);
        if (result != 0) {
            setError(STS_EINTERNAL, "Bucket not accessible: " + m_bucket_name);
            std::cout << "VastLSU: Bucket " << m_bucket_name << " not accessible" << std::endl;
            return STS_EINTERNAL;
        }

        // List all objects in the bucket to calculate actual usage
        std::vector<VastS3ObjectInfo> objects;
        result = s3_client->listObjects(m_bucket_name, "", objects, 1000);  // Use the 4-parameter version
        if (result != 0) {
            std::cout << "VastLSU: Warning - Could not list objects in bucket " << m_bucket_name << std::endl;
            // Continue with other updates even if object listing fails
        } else {
            // Calculate total used space and image count from actual S3 objects
            sts_uint64_t total_used = 0;
            sts_uint64_t image_count = 0;
            sts_uint64_t metadata_count = 0;
            
            for (const auto& obj : objects) {
                // Skip directory markers
                if (obj.key.back() == '/') {
                    continue;
                }
                
                // Categorize objects
                if (obj.key.find(".metadata") != std::string::npos) {
                    metadata_count++;
                } else if (obj.key.find("netbackup/") == 0) {
                    // NetBackup image data
                    total_used += obj.size;
                    if (obj.key.find("/data") != std::string::npos) {
                        image_count++;
                    }
                } else {
                    // Other data
                    total_used += obj.size;
                }
            }
            
            // Update capacity information with real values
            m_used_capacity = total_used;
            m_image_count = image_count;
            
            std::cout << "VastLSU: Calculated usage from " << objects.size() << " objects: "
                      << "Used=" << total_used << " bytes, Images=" << image_count << std::endl;
        }

        // Try to get additional info from VMS REST API
        VastRestClient* rest_client = m_server ? m_server->getRestClient() : nullptr;
        if (rest_client) {
            // Query VMS for view information that corresponds to this bucket
            std::vector<VastViewInfo> views;
            result = rest_client->listViews(views);
            if (result == 0) {
                for (const auto& view : views) {
                    // Check if this view corresponds to our bucket
                    if (view.name.find(m_bucket_name) != std::string::npos ||
                        view.path.find(m_bucket_name) != std::string::npos) {
                        
                        std::cout << "VastLSU: Found corresponding VMS view: " << view.name << std::endl;
                        
                        // Extract quota/capacity information from view properties
                        auto quota_it = view.properties.find("quota");
                        if (quota_it != view.properties.end()) {
                            try {
                                sts_uint64_t vms_capacity = std::stoull(quota_it->second);
                                if (vms_capacity > 0) {
                                    m_total_capacity = vms_capacity;
                                    std::cout << "VastLSU: Updated capacity from VMS: " << m_total_capacity << " bytes" << std::endl;
                                }
                            } catch (const std::exception& e) {
                                std::cout << "VastLSU: Could not parse quota from VMS: " << quota_it->second << std::endl;
                            }
                        }
                        
                        // Check view status
                        auto status_it = view.properties.find("status");
                        if (status_it != view.properties.end()) {
                            m_status = status_it->second;
                            std::cout << "VastLSU: Updated status from VMS: " << m_status << std::endl;
                        }
                        
                        // Check for tenant information
                        auto tenant_it = view.properties.find("tenant");
                        if (tenant_it != view.properties.end()) {
                            std::cout << "VastLSU: View tenant: " << tenant_it->second << std::endl;
                        }
                        
                        break;
                    }
                }
            } else {
                std::cout << "VastLSU: Could not query VMS for view information" << std::endl;
            }
            
            // Also try to get tenant information
            std::vector<VastTenantInfo> tenants;
            result = rest_client->listTenants(tenants);
            if (result == 0) {
                for (const auto& tenant : tenants) {
                    // Check if this tenant might be related to our LSU
                    if (tenant.name.find("netbackup") != std::string::npos) {
                        std::cout << "VastLSU: Found NetBackup tenant: " << tenant.name << std::endl;
                        break;
                    }
                }
            }
        }

        // Ensure capacity values are consistent
        if (m_total_capacity == 0 || m_total_capacity < m_used_capacity) {
            // Set capacity to a reasonable default if we couldn't get it from VMS
            m_total_capacity = std::max(
                static_cast<sts_uint64_t>(1024ULL * 1024ULL * 1024ULL * 1024ULL),  // 1 TB minimum
                m_used_capacity * 3                      // Or 3x used space
            );
            std::cout << "VastLSU: Set default capacity: " << m_total_capacity << " bytes" << std::endl;
        }
        
        m_available_capacity = (m_total_capacity > m_used_capacity) ? 
                              (m_total_capacity - m_used_capacity) : 0;

        // Update LSU flags based on current status
        m_lsu_flags = STS_LSUF_DISK | STS_LSUF_ACTIVE;
        if (m_status != "online") {
            m_lsu_flags |= STS_LSUF_DOWN;
        }

        std::cout << "VastLSU: Successfully updated LSU info for " << m_name << std::endl;
        std::cout << "  - Total Capacity: " << m_total_capacity << " bytes" << std::endl;
        std::cout << "  - Used Capacity: " << m_used_capacity << " bytes" << std::endl;
        std::cout << "  - Available: " << m_available_capacity << " bytes" << std::endl;
        std::cout << "  - Images: " << m_image_count << std::endl;
        std::cout << "  - Status: " << m_status << std::endl;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Update info failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::setLabel(const std::string& label)
{
    try {
        m_label = label;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to set label: ") + e.what());
        return STS_EINTERNAL;
    }
}

VastImage* VastLSU::createImage(const sts_image_def_v10_t* image_def, int flags)
{
    if (!image_def) {
        setError(STS_EINVAL, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->create(flags);
        if (result != STS_EOK) {
            setError(result, "Failed to create image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        m_image_count++;
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image creation failed: ") + e.what());
        return nullptr;
    }
}

VastImage* VastLSU::openImage(const sts_image_def_v10_t* image_def, int mode)
{
    if (!image_def) {
        setError(STS_EINVAL, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->open(mode);
        if (result != STS_EOK) {
            setError(result, "Failed to open image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image opening failed: ") + e.what());
        return nullptr;
    }
}

int VastLSU::deleteImage(const sts_image_def_v10_t* image_def, int async_flag)
{
    if (!image_def) {
        return STS_EINVAL;
    }

    try {
        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Generate S3 object key for the image
        std::string object_key = generateImageKey(image_def);

        // Delete the S3 object
        int result = s3_client->deleteObject(m_bucket_name, object_key);
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to delete S3 object: " + object_key);
            return STS_EINTERNAL;
        }

        // Also delete metadata object if it exists
        std::string metadata_key = object_key + ".metadata";
        s3_client->deleteObject(m_bucket_name, metadata_key); // Best effort, ignore errors

        std::cout << "VastLSU: Successfully deleted image " << image_def->img_basename << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image deletion failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::getImageList(std::vector<VastImage>& image_list)
{
    image_list.clear();
    
    try {
        // Get S3 client to list actual objects
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // List all objects in the bucket with NetBackup prefix
        std::vector<VastS3ObjectInfo> objects;
        std::string prefix = "netbackup/";
        
        int result = s3_client->listObjects(m_bucket_name, prefix, objects, 1000);  // Use 4-parameter version
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to list S3 objects");
            return STS_EINTERNAL;
        }

        std::cout << "VastLSU: Found " << objects.size() << " objects in bucket " << m_bucket_name << std::endl;

        // Process each object and create VastImage objects
        for (const auto& obj : objects) {
            // Skip metadata files and directories
            if (obj.key.find(".metadata") != std::string::npos || 
                obj.key.back() == '/') {
                continue;
            }

            // Parse object key to extract image information
            // Expected format: netbackup/{image_name}/{date}/data
            std::vector<std::string> path_parts;
            std::stringstream ss(obj.key);
            std::string part;
            
            while (std::getline(ss, part, '/')) {
                if (!part.empty()) {
                    path_parts.push_back(part);
                }
            }

            if (path_parts.size() >= 3 && path_parts[0] == "netbackup" && 
                path_parts[path_parts.size() - 1] == "data") {
                
                // Extract image name and date
                std::string image_name = path_parts[1];
                std::string date_str = path_parts[2];

                // Create image definition
                sts_image_def_v10_t image_def;
                memset(&image_def, 0, sizeof(image_def));
                
                strncpy(image_def.img_basename, image_name.c_str(), 
                       sizeof(image_def.img_basename) - 1);
                strncpy(image_def.img_date, date_str.c_str(), 
                       sizeof(image_def.img_date) - 1);
                
                // Set basic flags
                image_def.img_saveas = STS_IMG_FULL;  // Use STS_IMG_FULL as suggested by compiler
                image_def.img_flags = 0;

                // Create VastImage object
                VastImage image(this, &image_def);
                image.setSize(obj.size);
                
                image_list.push_back(image);
                
                std::cout << "VastLSU: Added image " << image_name 
                         << " (date: " << date_str << ", size: " << obj.size << ")" << std::endl;
            }
        }
        
        m_image_count = image_list.size();
        std::cout << "VastLSU: Retrieved " << image_list.size() << " images from S3" << std::endl;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to get image list: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::getImageInfo(const sts_image_def_v10_t* image_def, sts_image_info_v10_t* image_info)
{
    if (!image_def || !image_info) {
        return STS_EINVAL;
    }

    try {
        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Generate S3 object key for the image
        std::string object_key = generateImageKey(image_def);

        // Get object metadata from S3
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(m_bucket_name, object_key, object_info);
        if (result != 0) {
            setError(STS_EINVAL, "Image not found: " + std::string(image_def->img_basename));
            return STS_EINVAL;
        }

        // Fill in image info structure
        memset(image_info, 0, sizeof(sts_image_info_v10_t));
        image_info->version = 10;
        memcpy(&image_info->imo_def, image_def, sizeof(sts_image_def_v10_t));
        image_info->imo_size = object_info.size;
        image_info->imo_status = STS_II_IMAGE_CREATED | STS_II_FILES_CREATED;

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Get image info failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::getLSUInfo(sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        setError(STS_EINVAL, "Invalid LSU info pointer");
        return STS_EINVAL;
    }
    
    try {
        // Attempt to refresh capacity info from server first
        refreshCapacityInfo();
        
        // Fill in the LSU info structure
        memset(lsu_info, 0, sizeof(sts_lsu_info_v11_t));
        
        // Set LSU capacity information
        lsu_info->lsu_capacity = m_total_capacity;
        lsu_info->lsu_used = m_used_capacity;
        // Don't use lsu_free directly as it might not exist in all struct versions
        sts_uint64_t available_space = m_total_capacity - m_used_capacity;
        lsu_info->lsu_used_phys = m_used_capacity;
        lsu_info->lsu_images = m_image_count;
        
        // Set LSU definition information
        lsu_info->lsu_def.version = 11;  // Must match the version requested
        lsu_info->lsu_def.sld_alloc = STS_LSU_AT_STATIC;  // Static allocation
        lsu_info->lsu_def.sld_storage = STS_LSU_ST_FILE;  // File-based storage
        
        // Copy LSU name
        strncpy(lsu_info->lsu_def.sld_name.sln_name, 
                m_name.c_str(), 
                sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1);
        lsu_info->lsu_def.sld_name.sln_name[sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1] = '\0';
        
        // Set transfer and block size information
        lsu_info->lsu_def.sld_max_transfer = m_max_transfer_size;
        lsu_info->lsu_def.sld_block_size = m_block_size;
        lsu_info->lsu_def.sld_flags = m_lsu_flags;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to get LSU info: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::getLSUInfo(sts_lsu_info_v9_t* lsu_info)
{
    if (!lsu_info) {
        setError(STS_EINVAL, "Invalid LSU info pointer");
        return STS_EINVAL;
    }
    
    try {
        // Attempt to refresh capacity info from server first
        refreshCapacityInfo();
        
        // Fill in the LSU info structure
        memset(lsu_info, 0, sizeof(sts_lsu_info_v9_t));
        
        // Set LSU capacity information
        lsu_info->lsu_capacity = m_total_capacity;
        lsu_info->lsu_used = m_used_capacity;
        // Don't access lsu_free directly as it might not exist in all struct versions
        lsu_info->lsu_used_phys = m_used_capacity;
        lsu_info->lsu_images = m_image_count;
        
        // The struct layout may be different for v9 than v11, avoid using version field if unsure
        // lsu_info->lsu_def.version = 9;  // Removed this field access
        
        // Core required fields that definitely exist in LSU definition
        lsu_info->lsu_def.sld_alloc = STS_LSU_AT_STATIC;  // Static allocation
        lsu_info->lsu_def.sld_storage = STS_LSU_ST_FILE;  // File-based storage
        
        // Copy LSU name
        strncpy(lsu_info->lsu_def.sld_name.sln_name, 
                m_name.c_str(), 
                sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1);
        lsu_info->lsu_def.sld_name.sln_name[sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1] = '\0';
        
        // Set transfer and block size information
        lsu_info->lsu_def.sld_max_transfer = m_max_transfer_size;
        lsu_info->lsu_def.sld_block_size = m_block_size;
        lsu_info->lsu_def.sld_flags = m_lsu_flags;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to get LSU info: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::refreshCapacityInfo()
{
    std::cout << "VastLSU: Refreshing capacity info for LSU " << m_name << std::endl;
    
    try {
        if (!m_server) {
            setError(STS_EINTERNAL, "No server connection available");
            return STS_EINTERNAL;
        }

        // Get S3 client to calculate actual bucket usage
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            std::cout << "VastLSU: S3 client not available, using defaults" << std::endl;
            // Fall back to defaults if no S3 client
            if (m_total_capacity == 0) {
                m_total_capacity = 1024ULL * 1024ULL * 1024ULL * 1024ULL;  // 1 TB default
                m_available_capacity = m_total_capacity;
                m_used_capacity = 0;
            }
            return STS_EOK;
        }

        // Check if bucket exists
        int result = s3_client->headBucket(m_bucket_name);
        if (result != 0) {
            setError(STS_EINTERNAL, "Bucket not accessible: " + m_bucket_name);
            return STS_EINTERNAL;
        }

        // Calculate actual used space by listing all objects in bucket
        std::vector<VastS3ObjectInfo> objects;
        result = s3_client->listObjects(m_bucket_name, "", objects, 1000);
        if (result != 0) {
            std::cout << "VastLSU: Warning - Could not list objects to calculate usage" << std::endl;
            // Use defaults if we can't list objects
            if (m_total_capacity == 0) {
                m_total_capacity = 1024ULL * 1024ULL * 1024ULL * 1024ULL;  // 1 TB
                m_available_capacity = m_total_capacity;
                m_used_capacity = 0;
            }
            return STS_EOK;
        }

        // Calculate total used space from all objects
        sts_uint64_t total_used = 0;
        sts_uint64_t image_count = 0;
        
        for (const auto& obj : objects) {
            // Skip directory markers and metadata files
            if (obj.key.back() != '/' && obj.key.find(".metadata") == std::string::npos) {
                total_used += obj.size;
                
                // Count images (objects that represent actual backup data)
                if (obj.key.find("netbackup/") == 0 && obj.key.find("/data") != std::string::npos) {
                    image_count++;
                }
            }
        }

        // Try to get capacity info from VMS via REST API
        VastRestClient* rest_client = m_server->getRestClient();
        if (rest_client) {
            // Query VMS for bucket quota/capacity information
            std::vector<VastViewInfo> views;
            result = rest_client->listViews(views);
            if (result == 0) {
                // Look for view that corresponds to this bucket
                for (const auto& view : views) {
                    if (view.name.find(m_bucket_name) != std::string::npos ||
                        view.path.find(m_bucket_name) != std::string::npos) {
                        
                        // Try to extract quota from view properties
                        auto quota_it = view.properties.find("quota");
                        if (quota_it != view.properties.end()) {
                            try {
                                m_total_capacity = std::stoull(quota_it->second);
                                std::cout << "VastLSU: Got capacity from VMS: " << m_total_capacity << " bytes" << std::endl;
                            } catch (...) {
                                std::cout << "VastLSU: Could not parse quota from VMS" << std::endl;
                            }
                        }
                        break;
                    }
                }
            }
        }

        // Update capacity information with real values
        m_used_capacity = total_used;
        m_image_count = image_count;
        
        // If we couldn't get capacity from VMS, use a reasonable default
        if (m_total_capacity == 0 || m_total_capacity < m_used_capacity) {
            // Set capacity to at least twice the used space, minimum 1TB
            m_total_capacity = std::max(
                static_cast<sts_uint64_t>(1024ULL * 1024ULL * 1024ULL * 1024ULL),  // 1 TB minimum
                m_used_capacity * 2                      // Or twice used space
            );
        }
        
        m_available_capacity = (m_total_capacity > m_used_capacity) ? 
                              (m_total_capacity - m_used_capacity) : 0;

        std::cout << "VastLSU: Updated capacity info - Total: " << m_total_capacity 
                  << ", Used: " << m_used_capacity 
                  << ", Available: " << m_available_capacity 
                  << ", Images: " << m_image_count << std::endl;

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Failed to refresh capacity info: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastLSU::getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_EINVAL;
    }
    
    std::string config = "{\n";
    config += "  \"name\": \"" + m_name + "\",\n";
    config += "  \"bucket\": \"" + m_bucket_name + "\",\n";
    config += "  \"path\": \"" + m_path + "\",\n";
    config += "  \"status\": \"" + m_status + "\"\n";
    config += "}";
    
    *maxlen = static_cast<sts_uint32_t>(config.length());
    
    if (buflen < *maxlen + 1) {
        return STS_EMALLOC;
    }
    
    strncpy(buf, config.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';
    
    return STS_EOK;
}

int VastLSU::setConfig(const char* buf)
{
    if (!buf) {
        return STS_EINVAL;
    }

    std::cout << "VastLSU: Setting configuration for LSU " << m_name << std::endl;

    try {
        std::string config_str(buf);
        std::cout << "VastLSU: Received config: " << config_str << std::endl;

        // Parse JSON configuration to extract settings
        // Simple JSON parsing for key configuration items
        
        // Look for bucket name override
        size_t bucket_pos = config_str.find("\"bucket\":");
        if (bucket_pos != std::string::npos) {
            size_t start = config_str.find("\"", bucket_pos + 9);
            size_t end = config_str.find("\"", start + 1);
            if (start != std::string::npos && end != std::string::npos) {
                std::string new_bucket = config_str.substr(start + 1, end - start - 1);
                if (!new_bucket.empty() && new_bucket != m_bucket_name) {
                    std::cout << "VastLSU: Bucket name change requested: " << m_bucket_name 
                              << " -> " << new_bucket << std::endl;
                    
                    // Verify the new bucket exists via S3
                    VastS3Client* s3_client = getS3Client();
                    if (s3_client) {
                        int result = s3_client->headBucket(new_bucket);
                        if (result == 0) {
                            m_bucket_name = new_bucket;
                            m_path = "/" + m_bucket_name;
                            std::cout << "VastLSU: Successfully updated bucket name to " << new_bucket << std::endl;
                        } else {
                            setError(STS_EINVAL, "New bucket does not exist: " + new_bucket);
                            return STS_EINVAL;
                        }
                    }
                }
            }
        }

        // Look for capacity/quota settings - these should be set via VMS
        size_t quota_pos = config_str.find("\"quota\":");
        if (quota_pos != std::string::npos) {
            std::cout << "VastLSU: Quota setting requested - this should be configured through VMS" << std::endl;
            
            // Try to extract quota value and validate it
            size_t start = quota_pos + 8;
            size_t end = config_str.find_first_of(",}", start);
            if (end != std::string::npos) {
                std::string quota_str = config_str.substr(start, end - start);
                // Remove quotes and whitespace
                quota_str.erase(0, quota_str.find_first_not_of(" \t\""));
                quota_str.erase(quota_str.find_last_not_of(" \t\"") + 1);
                
                try {
                    sts_uint64_t new_quota = std::stoull(quota_str);
                    std::cout << "VastLSU: Quota change requested: " << new_quota << " bytes" << std::endl;
                    
                    // For quota changes, we should ideally call VMS API
                    VastRestClient* rest_client = m_server ? m_server->getRestClient() : nullptr;
                    if (rest_client) {
                        // In a real implementation, we would call VMS to update the view quota
                        std::cout << "VastLSU: Would update quota via VMS API (not implemented in this version)" << std::endl;
                        
                        // For now, just log the request
                        return STS_ENOTSUP; // Not supported - must be done through VMS
                    }
                } catch (const std::exception& e) {
                    setError(STS_EINVAL, "Invalid quota value: " + quota_str);
                    return STS_EINVAL;
                }
            }
        }

        // Look for transfer size configuration
        size_t transfer_pos = config_str.find("\"max_transfer\":");
        if (transfer_pos != std::string::npos) {
            size_t start = transfer_pos + 15;
            size_t end = config_str.find_first_of(",}", start);
            if (end != std::string::npos) {
                std::string transfer_str = config_str.substr(start, end - start);
                transfer_str.erase(0, transfer_str.find_first_not_of(" \t\""));
                transfer_str.erase(transfer_str.find_last_not_of(" \t\"") + 1);
                
                try {
                    sts_uint64_t new_transfer = std::stoull(transfer_str);
                    if (new_transfer > 0 && new_transfer <= (1024ULL * 1024 * 1024)) { // Max 1GB
                        m_max_transfer_size = new_transfer;
                        std::cout << "VastLSU: Updated max transfer size to " << new_transfer << " bytes" << std::endl;
                    } else {
                        setError(STS_EINVAL, "Invalid transfer size (must be 1-1GB): " + transfer_str);
                        return STS_EINVAL;
                    }
                } catch (const std::exception& e) {
                    setError(STS_EINVAL, "Invalid transfer size value: " + transfer_str);
                    return STS_EINVAL;
                }
            }
        }

        // Look for block size configuration
        size_t block_pos = config_str.find("\"block_size\":");
        if (block_pos != std::string::npos) {
            size_t start = block_pos + 13;
            size_t end = config_str.find_first_of(",}", start);
            if (end != std::string::npos) {
                std::string block_str = config_str.substr(start, end - start);
                block_str.erase(0, block_str.find_first_not_of(" \t\""));
                block_str.erase(block_str.find_last_not_of(" \t\"") + 1);
                
                try {
                    sts_uint64_t new_block = std::stoull(block_str);
                    if (new_block >= 512 && new_block <= (1024 * 1024)) { // 512B to 1MB
                        m_block_size = new_block;
                        std::cout << "VastLSU: Updated block size to " << new_block << " bytes" << std::endl;
                    } else {
                        setError(STS_EINVAL, "Invalid block size (must be 512B-1MB): " + block_str);
                        return STS_EINVAL;
                    }
                } catch (const std::exception& e) {
                    setError(STS_EINVAL, "Invalid block size value: " + block_str);
                    return STS_EINVAL;
                }
            }
        }

        std::cout << "VastLSU: Configuration update completed successfully" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Config parsing failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

VastS3Client* VastLSU::getS3Client()
{
    if (m_server) {
        return m_server->getS3Client();
    }
    return nullptr;
}

void VastLSU::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastLSU Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastLSU::clearError()
{
    m_last_error = STS_EOK;
    m_last_error_msg.clear();
}

std::string VastLSU::generateImageKey(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return "";
    }
    
    return std::string(image_def->img_basename) + "_" + std::string(image_def->img_date);
}

int VastLSU::validateImageDefinition(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return STS_EINVAL;
    }
    
    if (strlen(image_def->img_basename) == 0) {
        return STS_EINVAL;
    }
    
    return STS_EOK;
}
