#
# Vast Data OST Plugin Makefile
# For NetBackup OST SDK 11.1.1
#

# Platform detection (following OST SDK sample pattern)
UNAME_S := $(shell uname -s)
UNAME_M := $(shell uname -m)

# Set platform based on OS and architecture
ifeq ($(UNAME_S),Linux)
    ifeq ($(UNAME_M),x86_64)
        PLATFORM = linuxR_x64
    endif
endif

# Default to linuxR_x64 if not detected
ifndef PLATFORM
    PLATFORM = linuxR_x64
endif

# OST SDK base path (relative to this directory)
OST_SDK_BASE = ../OST-SDK-11.1.1

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -fPIC -Wall -Wextra -O2 -DVAST_PGN_EXPORTS -DSTS_EXTERNAL_VENDOR

# Include paths - OST SDK headers first, then current directory
INCLUDES = -I$(OST_SDK_BASE)/src/include -I$(OST_SDK_BASE)/src/include/platforms/$(PLATFORM) -I.

# Library paths and libraries
LIBPATH = -L/usr/lib -L/usr/local/lib
LIBS = -lcurl -lssl -lcrypto -ljsoncpp -lpthread -ldl -lstdc++

# Source files
SOURCES = vastplugin.cpp VastStorageServer.cpp VastRestClient.cpp VastS3Client.cpp VastLSU.cpp VastImage.cpp

# Object files
OBJECTS = $(SOURCES:.cpp=.o)

# Target library
TARGET = libvastost.so

# Default target
all: $(TARGET)

# Build the shared library
$(TARGET): $(OBJECTS)
	$(CXX) -shared -o $@ $^ $(LIBPATH) $(LIBS)

# Compile source files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(TARGET)

# Install (you may need to adjust paths)
install: $(TARGET)
	cp $(TARGET) /usr/local/lib/
	mkdir -p /usr/local/include/vastost/
	cp VastStorageServer.h VastRestClient.h VastS3Client.h VastLSU.h VastImage.h vastplugin.h /usr/local/include/vastost/
	mkdir -p /etc/vastost/
	cp vast_config.conf /etc/vastost/

# Print configuration
config:
	@echo "Platform: $(PLATFORM)"
	@echo "OST SDK Base: $(OST_SDK_BASE)"
	@echo "Compiler: $(CXX)"
	@echo "Flags: $(CXXFLAGS)"
	@echo "Includes: $(INCLUDES)"
	@echo "Libraries: $(LIBS)"
	@echo "SDK Headers Available:"
	@ls -la $(OST_SDK_BASE)/src/include/ 2>/dev/null || echo "  SDK not found"
	@ls -la $(OST_SDK_BASE)/src/include/platforms/$(PLATFORM)/ 2>/dev/null || echo "  Platform headers not found"

# Verify SDK availability
check-sdk:
	@echo "Checking OST SDK availability..."
	@test -d $(OST_SDK_BASE)/src/include || (echo "ERROR: OST SDK not found at $(OST_SDK_BASE)" && exit 1)
	@test -f $(OST_SDK_BASE)/src/include/stspi.h || (echo "ERROR: stspi.h not found" && exit 1)
	@test -f $(OST_SDK_BASE)/src/include/platforms/$(PLATFORM)/stsplat.h || (echo "ERROR: Platform headers not found for $(PLATFORM)" && exit 1)
	@echo "OST SDK found and ready"

.PHONY: all clean install config check-sdk

# Notes:
# - Requires libcurl for HTTP operations
# - Requires OpenSSL for HTTPS/encryption  
# - Requires jsoncpp for JSON parsing
# - OST SDK headers must be available at $(OST_SDK_BASE)/src/include