/*
 *************************************************************************
 * Vast Data REST Client
 * For API communication with Vast Data REST API
 *************************************************************************
 */

#ifndef _VAST_REST_CLIENT_H_
#define _VAST_REST_CLIENT_H_

#include <string>
#include <vector>
#include <map>
#include "VastCommonTypes.h"  // Include common types and constants

// Forward declarations
class VastStorageServer;

// The VastS3KeyPair struct is now defined in VastCommonTypes.h

class VastRestClient {
public:
    VastRestClient();
    ~VastRestClient();

    // Connection management
    int initialize(const std::string& vms_endpoint, bool verify_ssl = true);
    void setDefaultHeaders(const std::map<std::string, std::string>& headers);
    void setTimeout(int timeout_seconds);

    // Authentication API calls
    int authenticate(const std::string& username, const std::string& password, VastAuthTokens& tokens);
    int refreshToken(const std::string& refresh_token, VastAuthTokens& tokens);
    int logout();
    void setAuthToken(const std::string& token);

    // VMS System Information
    int getVmsInfo(std::map<std::string, std::string>& vms_info);
    int getClusterStatus(std::map<std::string, std::string>& cluster_status);

    // Views Management (NFS/SMB volumes)
    int listViews(std::vector<VastViewInfo>& views);
    int getViewInfo(const std::string& view_name, VastViewInfo& view_info);
    int createView(const VastViewInfo& view_info);
    int deleteView(const std::string& view_name);

    // S3 Key Management (Correct Vast Data API - /api/s3keys/)
    int listS3Keys(std::vector<VastS3Key>& s3_keys);                  // GET /api/s3keys/
    int getS3Key(const std::string& key_name, VastS3Key& s3_key);     // GET /api/s3keys/{name}/
    int createS3Key(const std::string& name, const std::string& user_name, VastS3KeyPair& key_pair);  // POST /api/s3keys/
    int getS3Keys(std::vector<VastS3KeyPair>& keys);                  // GET /api/s3keys/ (alternative signature)
    int deleteS3Key(const std::string& access_key);                   // DELETE /api/s3keys/{access_key}/
    
    // Tenant Management (Correct Vast Data API - /api/tenants/)
    int listTenants(std::vector<VastTenantInfo>& tenants);            // GET /api/tenants/
    int getTenantInfo(const std::string& tenant_name, VastTenantInfo& tenant_info);  // GET /api/tenants/{name}/
    int createTenant(const VastTenantInfo& tenant_info);              // POST /api/tenants/
    int deleteTenant(const std::string& tenant_name);                 // DELETE /api/tenants/{name}/

    // Image metadata management
    int updateImageMetadata(const std::string& bucket_name, const std::string& object_key,
                           const ImageDefinition& image_def);

    // Generic API methods
    int get(const std::string& endpoint, VastApiResponse& response);
    int post(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int put(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int patch(const std::string& endpoint, const std::string& json_body, VastApiResponse& response);
    int delete_request(const std::string& endpoint, VastApiResponse& response);

    // Cleanup
    void cleanup();

    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

private:
    std::string m_vms_endpoint;
    std::string m_auth_token;
    std::map<std::string, std::string> m_default_headers;
    int m_timeout_seconds;
    bool m_verify_ssl;

    // Authentication state
    bool m_authenticated;
    VastAuthTokens m_auth_tokens;

    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;

    // HTTP client implementation (could use libcurl, etc.)
    void* m_http_client;

    // Helper methods
    std::string buildUrl(const std::string& endpoint) const;
    int makeHttpRequest(const std::string& method, const std::string& url, 
                       const std::string& body, VastApiResponse& response);
    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string escapeJsonString(const std::string& input);
};

#endif /* _VAST_REST_CLIENT_H_ */