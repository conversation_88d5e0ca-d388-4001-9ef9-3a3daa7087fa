cmake_minimum_required(VERSION 3.10)
project(VastOSTPlugin VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# OST SDK paths (relative to this directory)
set(OST_SDK_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../OST-SDK-11.1.1")
set(OST_SDK_INCLUDE_DIR "${OST_SDK_ROOT}/src/include")

# Platform detection (following OST SDK pattern)
if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(OST_PLATFORM "linuxR_x64")
    elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64")
        set(OST_PLATFORM "linuxR_arm64")
    endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "AMD64")
        set(OST_PLATFORM "AMD64")
    endif()
endif()

# Default to Linux x64 if not detected
if(NOT OST_PLATFORM)
    set(OST_PLATFORM "linuxR_x64")
endif()

set(OST_PLATFORM_INCLUDE_DIR "${OST_SDK_INCLUDE_DIR}/platforms/${OST_PLATFORM}")

# Check if OST SDK exists
if(NOT EXISTS "${OST_SDK_INCLUDE_DIR}/stspi.h")
    message(FATAL_ERROR "OST SDK not found at ${OST_SDK_ROOT}. Please ensure the SDK is extracted to the correct location.")
endif()

if(NOT EXISTS "${OST_PLATFORM_INCLUDE_DIR}/stsplat.h")
    message(FATAL_ERROR "Platform-specific headers not found at ${OST_PLATFORM_INCLUDE_DIR}")
endif()

# Include directories - OST SDK headers first, then current directory
include_directories(
    ${OST_SDK_INCLUDE_DIR}
    ${OST_PLATFORM_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Add compile definitions for OST plugin
add_compile_definitions(
    VAST_PGN_EXPORTS
    STS_EXTERNAL_VENDOR
)

# Find required libraries
find_package(PkgConfig REQUIRED)

# Find curl for REST API
find_package(CURL REQUIRED)

# Find OpenSSL for S3 operations
find_package(OpenSSL REQUIRED)

# Find JSON library (jsoncpp)
pkg_check_modules(JSONCPP REQUIRED jsoncpp)

# Source files
set(VAST_PLUGIN_SOURCES
    vastplugin.cpp
    VastStorageServer.cpp
    VastRestClient.cpp
    VastS3Client.cpp
    VastLSU.cpp
    VastImage.cpp
)

# Header files
set(VAST_PLUGIN_HEADERS
    vastplugin.h
    VastStorageServer.h
    VastRestClient.h
    VastS3Client.h
    VastLSU.h
    VastImage.h
)

# Create shared library (OST plugin)
add_library(vastost SHARED ${VAST_PLUGIN_SOURCES})

# Set library properties
set_target_properties(vastost PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
)

# Link libraries
target_link_libraries(vastost
    CURL::libcurl
    OpenSSL::SSL
    OpenSSL::Crypto
    pthread
    dl
)

# Link jsoncpp
target_link_libraries(vastost ${JSONCPP_LIBRARIES})
target_include_directories(vastost PRIVATE ${JSONCPP_INCLUDE_DIRS})

# Install targets
install(TARGETS vastost
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${VAST_PLUGIN_HEADERS}
    DESTINATION include/vastost
)

# Install configuration file
install(FILES vast_config.conf
    DESTINATION etc/vastost
)

# Print configuration summary
message(STATUS "")
message(STATUS "Vast Data OST Plugin Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OST SDK path: ${OST_SDK_ROOT}")
message(STATUS "  Platform: ${OST_PLATFORM}")
message(STATUS "  Platform headers: ${OST_PLATFORM_INCLUDE_DIR}")
message(STATUS "  CURL found: ${CURL_FOUND}")
message(STATUS "  jsoncpp found: ${JSONCPP_FOUND}")
message(STATUS "")