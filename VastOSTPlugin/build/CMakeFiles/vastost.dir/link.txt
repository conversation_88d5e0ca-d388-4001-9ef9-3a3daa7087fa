/usr/bin/c++ -fPIC -O3 -DNDEBUG -shared -Wl,-soname,libvastost.so.1 -o libvastost.so.1.0.0 CMakeFiles/vastost.dir/vastplugin.cpp.o CMakeFiles/vastost.dir/VastStorageServer.cpp.o CMakeFiles/vastost.dir/VastRestClient.cpp.o CMakeFiles/vastost.dir/VastS3Client.cpp.o CMakeFiles/vastost.dir/VastLSU.cpp.o CMakeFiles/vastost.dir/VastImage.cpp.o  /usr/lib/x86_64-linux-gnu/libcurl.so /usr/lib/x86_64-linux-gnu/libssl.so /usr/lib/x86_64-linux-gnu/libcrypto.so -lpthread -ldl -ljsoncpp 
