# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build

# Include any dependencies generated for this target.
include CMakeFiles/vastost.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/vastost.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vastost.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/vastost.dir/flags.make

CMakeFiles/vastost.dir/vastplugin.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/vastplugin.cpp.o: ../vastplugin.cpp
CMakeFiles/vastost.dir/vastplugin.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/vastost.dir/vastplugin.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/vastplugin.cpp.o -MF CMakeFiles/vastost.dir/vastplugin.cpp.o.d -o CMakeFiles/vastost.dir/vastplugin.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/vastplugin.cpp

CMakeFiles/vastost.dir/vastplugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/vastplugin.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/vastplugin.cpp > CMakeFiles/vastost.dir/vastplugin.cpp.i

CMakeFiles/vastost.dir/vastplugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/vastplugin.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/vastplugin.cpp -o CMakeFiles/vastost.dir/vastplugin.cpp.s

CMakeFiles/vastost.dir/VastStorageServer.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/VastStorageServer.cpp.o: ../VastStorageServer.cpp
CMakeFiles/vastost.dir/VastStorageServer.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/vastost.dir/VastStorageServer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/VastStorageServer.cpp.o -MF CMakeFiles/vastost.dir/VastStorageServer.cpp.o.d -o CMakeFiles/vastost.dir/VastStorageServer.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastStorageServer.cpp

CMakeFiles/vastost.dir/VastStorageServer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/VastStorageServer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastStorageServer.cpp > CMakeFiles/vastost.dir/VastStorageServer.cpp.i

CMakeFiles/vastost.dir/VastStorageServer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/VastStorageServer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastStorageServer.cpp -o CMakeFiles/vastost.dir/VastStorageServer.cpp.s

CMakeFiles/vastost.dir/VastRestClient.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/VastRestClient.cpp.o: ../VastRestClient.cpp
CMakeFiles/vastost.dir/VastRestClient.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/vastost.dir/VastRestClient.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/VastRestClient.cpp.o -MF CMakeFiles/vastost.dir/VastRestClient.cpp.o.d -o CMakeFiles/vastost.dir/VastRestClient.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastRestClient.cpp

CMakeFiles/vastost.dir/VastRestClient.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/VastRestClient.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastRestClient.cpp > CMakeFiles/vastost.dir/VastRestClient.cpp.i

CMakeFiles/vastost.dir/VastRestClient.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/VastRestClient.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastRestClient.cpp -o CMakeFiles/vastost.dir/VastRestClient.cpp.s

CMakeFiles/vastost.dir/VastS3Client.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/VastS3Client.cpp.o: ../VastS3Client.cpp
CMakeFiles/vastost.dir/VastS3Client.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/vastost.dir/VastS3Client.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/VastS3Client.cpp.o -MF CMakeFiles/vastost.dir/VastS3Client.cpp.o.d -o CMakeFiles/vastost.dir/VastS3Client.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastS3Client.cpp

CMakeFiles/vastost.dir/VastS3Client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/VastS3Client.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastS3Client.cpp > CMakeFiles/vastost.dir/VastS3Client.cpp.i

CMakeFiles/vastost.dir/VastS3Client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/VastS3Client.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastS3Client.cpp -o CMakeFiles/vastost.dir/VastS3Client.cpp.s

CMakeFiles/vastost.dir/VastLSU.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/VastLSU.cpp.o: ../VastLSU.cpp
CMakeFiles/vastost.dir/VastLSU.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/vastost.dir/VastLSU.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/VastLSU.cpp.o -MF CMakeFiles/vastost.dir/VastLSU.cpp.o.d -o CMakeFiles/vastost.dir/VastLSU.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastLSU.cpp

CMakeFiles/vastost.dir/VastLSU.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/VastLSU.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastLSU.cpp > CMakeFiles/vastost.dir/VastLSU.cpp.i

CMakeFiles/vastost.dir/VastLSU.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/VastLSU.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastLSU.cpp -o CMakeFiles/vastost.dir/VastLSU.cpp.s

CMakeFiles/vastost.dir/VastImage.cpp.o: CMakeFiles/vastost.dir/flags.make
CMakeFiles/vastost.dir/VastImage.cpp.o: ../VastImage.cpp
CMakeFiles/vastost.dir/VastImage.cpp.o: CMakeFiles/vastost.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/vastost.dir/VastImage.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vastost.dir/VastImage.cpp.o -MF CMakeFiles/vastost.dir/VastImage.cpp.o.d -o CMakeFiles/vastost.dir/VastImage.cpp.o -c /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastImage.cpp

CMakeFiles/vastost.dir/VastImage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vastost.dir/VastImage.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastImage.cpp > CMakeFiles/vastost.dir/VastImage.cpp.i

CMakeFiles/vastost.dir/VastImage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vastost.dir/VastImage.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastImage.cpp -o CMakeFiles/vastost.dir/VastImage.cpp.s

# Object files for target vastost
vastost_OBJECTS = \
"CMakeFiles/vastost.dir/vastplugin.cpp.o" \
"CMakeFiles/vastost.dir/VastStorageServer.cpp.o" \
"CMakeFiles/vastost.dir/VastRestClient.cpp.o" \
"CMakeFiles/vastost.dir/VastS3Client.cpp.o" \
"CMakeFiles/vastost.dir/VastLSU.cpp.o" \
"CMakeFiles/vastost.dir/VastImage.cpp.o"

# External object files for target vastost
vastost_EXTERNAL_OBJECTS =

libvastost.so.1.0.0: CMakeFiles/vastost.dir/vastplugin.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/VastStorageServer.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/VastRestClient.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/VastS3Client.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/VastLSU.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/VastImage.cpp.o
libvastost.so.1.0.0: CMakeFiles/vastost.dir/build.make
libvastost.so.1.0.0: /usr/lib/x86_64-linux-gnu/libcurl.so
libvastost.so.1.0.0: /usr/lib/x86_64-linux-gnu/libssl.so
libvastost.so.1.0.0: /usr/lib/x86_64-linux-gnu/libcrypto.so
libvastost.so.1.0.0: CMakeFiles/vastost.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX shared library libvastost.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/vastost.dir/link.txt --verbose=$(VERBOSE)
	$(CMAKE_COMMAND) -E cmake_symlink_library libvastost.so.1.0.0 libvastost.so.1 libvastost.so

libvastost.so.1: libvastost.so.1.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate libvastost.so.1

libvastost.so: libvastost.so.1.0.0
	@$(CMAKE_COMMAND) -E touch_nocreate libvastost.so

# Rule to build all files generated by this target.
CMakeFiles/vastost.dir/build: libvastost.so
.PHONY : CMakeFiles/vastost.dir/build

CMakeFiles/vastost.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/vastost.dir/cmake_clean.cmake
.PHONY : CMakeFiles/vastost.dir/clean

CMakeFiles/vastost.dir/depend:
	cd /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles/vastost.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/vastost.dir/depend

