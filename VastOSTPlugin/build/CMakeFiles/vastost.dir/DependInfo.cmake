
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastImage.cpp" "CMakeFiles/vastost.dir/VastImage.cpp.o" "gcc" "CMakeFiles/vastost.dir/VastImage.cpp.o.d"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastLSU.cpp" "CMakeFiles/vastost.dir/VastLSU.cpp.o" "gcc" "CMakeFiles/vastost.dir/VastLSU.cpp.o.d"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastRestClient.cpp" "CMakeFiles/vastost.dir/VastRestClient.cpp.o" "gcc" "CMakeFiles/vastost.dir/VastRestClient.cpp.o.d"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastS3Client.cpp" "CMakeFiles/vastost.dir/VastS3Client.cpp.o" "gcc" "CMakeFiles/vastost.dir/VastS3Client.cpp.o.d"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/VastStorageServer.cpp" "CMakeFiles/vastost.dir/VastStorageServer.cpp.o" "gcc" "CMakeFiles/vastost.dir/VastStorageServer.cpp.o.d"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/vastplugin.cpp" "CMakeFiles/vastost.dir/vastplugin.cpp.o" "gcc" "CMakeFiles/vastost.dir/vastplugin.cpp.o.d"
  )

# Pairs of files generated by the same build rule.
set(CMAKE_MULTIPLE_OUTPUT_PAIRS
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/libvastost.so" "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/libvastost.so.1.0.0"
  "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/libvastost.so.1" "/home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/libvastost.so.1.0.0"
  )


# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
