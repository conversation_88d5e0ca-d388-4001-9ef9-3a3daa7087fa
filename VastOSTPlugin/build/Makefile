# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named vastost

# Build rule for target.
vastost: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vastost
.PHONY : vastost

# fast build rule for target.
vastost/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/build
.PHONY : vastost/fast

VastImage.o: VastImage.cpp.o
.PHONY : VastImage.o

# target to build an object file
VastImage.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastImage.cpp.o
.PHONY : VastImage.cpp.o

VastImage.i: VastImage.cpp.i
.PHONY : VastImage.i

# target to preprocess a source file
VastImage.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastImage.cpp.i
.PHONY : VastImage.cpp.i

VastImage.s: VastImage.cpp.s
.PHONY : VastImage.s

# target to generate assembly for a file
VastImage.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastImage.cpp.s
.PHONY : VastImage.cpp.s

VastLSU.o: VastLSU.cpp.o
.PHONY : VastLSU.o

# target to build an object file
VastLSU.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastLSU.cpp.o
.PHONY : VastLSU.cpp.o

VastLSU.i: VastLSU.cpp.i
.PHONY : VastLSU.i

# target to preprocess a source file
VastLSU.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastLSU.cpp.i
.PHONY : VastLSU.cpp.i

VastLSU.s: VastLSU.cpp.s
.PHONY : VastLSU.s

# target to generate assembly for a file
VastLSU.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastLSU.cpp.s
.PHONY : VastLSU.cpp.s

VastRestClient.o: VastRestClient.cpp.o
.PHONY : VastRestClient.o

# target to build an object file
VastRestClient.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastRestClient.cpp.o
.PHONY : VastRestClient.cpp.o

VastRestClient.i: VastRestClient.cpp.i
.PHONY : VastRestClient.i

# target to preprocess a source file
VastRestClient.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastRestClient.cpp.i
.PHONY : VastRestClient.cpp.i

VastRestClient.s: VastRestClient.cpp.s
.PHONY : VastRestClient.s

# target to generate assembly for a file
VastRestClient.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastRestClient.cpp.s
.PHONY : VastRestClient.cpp.s

VastS3Client.o: VastS3Client.cpp.o
.PHONY : VastS3Client.o

# target to build an object file
VastS3Client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastS3Client.cpp.o
.PHONY : VastS3Client.cpp.o

VastS3Client.i: VastS3Client.cpp.i
.PHONY : VastS3Client.i

# target to preprocess a source file
VastS3Client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastS3Client.cpp.i
.PHONY : VastS3Client.cpp.i

VastS3Client.s: VastS3Client.cpp.s
.PHONY : VastS3Client.s

# target to generate assembly for a file
VastS3Client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastS3Client.cpp.s
.PHONY : VastS3Client.cpp.s

VastStorageServer.o: VastStorageServer.cpp.o
.PHONY : VastStorageServer.o

# target to build an object file
VastStorageServer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastStorageServer.cpp.o
.PHONY : VastStorageServer.cpp.o

VastStorageServer.i: VastStorageServer.cpp.i
.PHONY : VastStorageServer.i

# target to preprocess a source file
VastStorageServer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastStorageServer.cpp.i
.PHONY : VastStorageServer.cpp.i

VastStorageServer.s: VastStorageServer.cpp.s
.PHONY : VastStorageServer.s

# target to generate assembly for a file
VastStorageServer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/VastStorageServer.cpp.s
.PHONY : VastStorageServer.cpp.s

vastplugin.o: vastplugin.cpp.o
.PHONY : vastplugin.o

# target to build an object file
vastplugin.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/vastplugin.cpp.o
.PHONY : vastplugin.cpp.o

vastplugin.i: vastplugin.cpp.i
.PHONY : vastplugin.i

# target to preprocess a source file
vastplugin.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/vastplugin.cpp.i
.PHONY : vastplugin.cpp.i

vastplugin.s: vastplugin.cpp.s
.PHONY : vastplugin.s

# target to generate assembly for a file
vastplugin.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vastost.dir/build.make CMakeFiles/vastost.dir/vastplugin.cpp.s
.PHONY : vastplugin.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... vastost"
	@echo "... VastImage.o"
	@echo "... VastImage.i"
	@echo "... VastImage.s"
	@echo "... VastLSU.o"
	@echo "... VastLSU.i"
	@echo "... VastLSU.s"
	@echo "... VastRestClient.o"
	@echo "... VastRestClient.i"
	@echo "... VastRestClient.s"
	@echo "... VastS3Client.o"
	@echo "... VastS3Client.i"
	@echo "... VastS3Client.s"
	@echo "... VastStorageServer.o"
	@echo "... VastStorageServer.i"
	@echo "... VastStorageServer.s"
	@echo "... vastplugin.o"
	@echo "... vastplugin.i"
	@echo "... vastplugin.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

