/*
 *************************************************************************
 * Vast Data LSU (Logical Storage Unit) Class
 * Represents a Vast Data volume/bucket for NetBackup storage
 *************************************************************************
 */

#ifndef _VAST_LSU_H_
#define _VAST_LSU_H_

#include "../OST-SDK-11.1.1/src/include/stsi.h"
#include <string>
#include <vector>
#include <memory>

// Forward declarations
class VastStorageServer;
class VastImage;
class VastS3Client;

class VastLSU {
public:
    VastLSU(); // Default constructor for temporary objects
    VastLSU(const std::string& name, VastStorageServer* server);
    ~VastLSU();

    // LSU properties
    std::string getName() const { return m_name; }
    std::string getBucketName() const { return m_bucket_name; }
    std::string getPath() const { return m_path; }
    
    // Capacity information
    sts_uint64_t getCapacity() const { return m_total_capacity; }
    sts_uint64_t getUsedCapacity() const { return m_used_capacity; }
    sts_uint64_t getUsedSpace() const { return m_used_capacity; }
    sts_uint64_t getAvailableCapacity() const { return m_available_capacity; }
    sts_uint64_t getPhysicalSpace() const { return m_used_capacity; }
    sts_uint64_t getImageCount() const { return m_image_count; }
    
    // Status
    std::string getStatus() const { return m_status; }
    bool isOnline() const { return m_status == "online"; }

    // LSU operations
    int initialize(const std::string& bucket_name = "");
    int updateInfo();
    int setLabel(const std::string& label);
    std::string getLabel() const { return m_label; }

    // Image management
    VastImage* createImage(const sts_image_def_v10_t* image_def, int flags);
    VastImage* openImage(const sts_image_def_v10_t* image_def, int mode);
    int deleteImage(const sts_image_def_v10_t* image_def, int async_flag);
    int getImageList(std::vector<VastImage>& image_list);
    int getImageByName(const std::string& name, VastImage& image);
    int getImageInfo(const sts_image_def_v10_t* image_def, sts_image_info_v10_t* image_info);

    // LSU info for OST API
    int getLSUInfo(sts_lsu_info_v11_t* lsu_info);
    int getLSUInfo(sts_lsu_info_v9_t* lsu_info);

    // Configuration
    int getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen);
    int setConfig(const char* buf);

    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

    // Internal methods
    VastStorageServer* getServer() { return m_server; }
    VastStorageServer* getStorageServer() { return m_server; }  // Alias for getServer
    VastS3Client* getS3Client();

private:
    // Basic properties
    std::string m_name;
    std::string m_bucket_name;
    std::string m_path;
    std::string m_label;
    std::string m_status;
    
    // Capacity information
    sts_uint64_t m_total_capacity;
    sts_uint64_t m_used_capacity;
    sts_uint64_t m_available_capacity;
    sts_uint64_t m_image_count;
    
    // Server reference
    VastStorageServer* m_server;
    
    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;
    
    // Configuration
    sts_uint64_t m_max_transfer_size;
    sts_uint64_t m_block_size;
    sts_uint32_t m_lsu_flags;
    
    // Image cache
    std::vector<VastImage*> m_image_cache;
    
    // Helper methods
    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string generateImageKey(const sts_image_def_v10_t* image_def);
    int refreshCapacityInfo();
    int validateImageDefinition(const sts_image_def_v10_t* image_def);
    void initDefaults();
};

#endif /* _VAST_LSU_H_ */
