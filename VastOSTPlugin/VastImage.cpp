/*
 *************************************************************************
 * Vast Data Image Implementation
 * Represents a NetBackup backup image stored in Vast Data S3
 *************************************************************************
 */

#include "VastImage.h"
#include "VastLSU.h"
#include "VastRestClient.h"
#include "VastS3Client.h"
#include "VastStorageServer.h" // Include VastStorageServer.h to fix incomplete type error
#include <cstring>
#include <syslog.h> // Add syslog header for logging
#include <fstream>
#include <iostream>
#include <memory>
#include <sstream>
#include <ctime>
#include <chrono>
#include <iomanip>

using namespace std;

// Constants now come from VastCommonTypes.h via the VastImage.h include

// Helper function to get current timestamp
std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    return ss.str();
}

VastImage::VastImage()
    : m_size(0),
      m_block_size(STS_BLOCK_SIZE),
      m_current_size(0),
      m_timestamp(0),
      m_backup_time(0),
      m_copy_number(0),
      m_stream_number(0),
      m_fragment_number(0),
      m_is_open(false),
      m_is_pending(false),
      m_is_complete(false),
      m_mode(0),
      m_image_flags(0),
      m_image_size(0),
      m_lsu(nullptr),
      m_last_error(STS_EOK),
      m_metadata_dirty(false)
{
    memset(&m_image_def, 0, sizeof(m_image_def));
    initDefaults();
}

VastImage::VastImage(VastLSU* lsu, const sts_image_def_v10_t* image_def)
    : m_size(0),
      m_block_size(STS_BLOCK_SIZE),
      m_current_size(0),
      m_timestamp(0), // Initialize timestamp to 0 first, will set from string later
      m_backup_time(0),
      m_copy_number(0),
      m_stream_number(0),
      m_fragment_number(0),
      m_is_open(false),
      m_is_pending(false),
      m_is_complete(false),
      m_mode(0),
      m_lsu(lsu),
      m_last_error(STS_EOK),
      m_metadata_dirty(false)
{
    if (image_def) {
        memcpy(&m_image_def, image_def, sizeof(m_image_def));
        m_image_name = image_def->img_basename;
        
        // Convert date string to timestamp if possible
        if (image_def->img_date[0] != '\0') {
            // Try to convert date string to timestamp
            // For now, just use a simple conversion or current time
            m_timestamp = static_cast<sts_uint64_t>(time(nullptr));
        }
    } else {
        memset(&m_image_def, 0, sizeof(m_image_def));
    }
    
    if (lsu) {
        m_lsu_name = lsu->getName();
    }
    
    initDefaults();
}

VastImage::~VastImage()
{
    // Close the image if still open
    if (m_is_open) {
        close(0, 1);  // Force close without completing
    }
}

void VastImage::initDefaults()
{
    // Set default values for any uninitialized members
    if (m_block_size == 0) {
        m_block_size = STS_BLOCK_SIZE;
    }
    
    // If timestamp is not set, use current time
    if (m_timestamp == 0) {
        m_timestamp = static_cast<sts_uint64_t>(time(nullptr));
    }
}

void VastImage::setBasename(const char* basename)
{
    if (basename) {
        m_image_name = basename;
        strncpy(m_image_def.img_basename, basename, sizeof(m_image_def.img_basename) - 1);
        m_image_def.img_basename[sizeof(m_image_def.img_basename) - 1] = '\0';
    }
}

void VastImage::setTimestamp(sts_uint64_t timestamp)
{
    m_timestamp = timestamp;
    
    // Convert timestamp to string for img_date
    snprintf(m_image_def.img_date, sizeof(m_image_def.img_date), "%llu", 
             static_cast<unsigned long long>(timestamp));
}

void VastImage::setLSUName(const std::string& lsu_name)
{
    m_lsu_name = lsu_name;
}

void VastImage::setMasterServer(const char* master_server)
{
    if (master_server) {
        m_master_server = master_server;
    }
}

void VastImage::setBackupTime(sts_uint64_t time)
{
    m_backup_time = time;
}

void VastImage::setCopyNumber(int copy_number)
{
    m_copy_number = copy_number;
}

void VastImage::setStreamNumber(int stream_number)
{
    m_stream_number = stream_number;
}

void VastImage::setFragmentNumber(int frag_number)
{
    m_fragment_number = frag_number;
}

ImageInfo* VastImage::getInfo()
{
    // Update and return the image info structure
    updateInfo();
    return &m_image_info;
}

int VastImage::write(void* buf, size_t length, size_t offset, sts_uint64_t* num_written)
{
    // Delegate to the actual writeImage implementation
    if (!num_written) {
        return STS_EINVAL;
    }
    
    return writeImage(buf, static_cast<sts_uint64_t>(length), 
                     static_cast<sts_uint64_t>(offset), num_written);
}

size_t VastImage::read(void* buf, size_t length, size_t offset)
{
    // Delegate to the actual readImage implementation
    if (!buf) {
        return 0;
    }
    
    sts_uint64_t bytes_read = 0;
    int result = readImage(buf, static_cast<sts_uint64_t>(length), 
                          static_cast<sts_uint64_t>(offset), &bytes_read);
    
    if (result != STS_EOK) {
        return 0;
    }
    
    return static_cast<size_t>(bytes_read);
}

int VastImage::remove()
{
    try {
        // Check if we have a valid LSU
        if (!m_lsu) {
            setError(STS_EINVAL, "No LSU provided");
            return STS_EINVAL;
        }

        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        std::string bucket_name = getBucketName();
        if (bucket_name.empty()) {
            setError(STS_EINTERNAL, "Bucket name not available");
            return STS_EINTERNAL;
        }

        // Delete the main image object
        if (!m_s3_key.empty()) {
            int result = s3_client->deleteObject(bucket_name, m_s3_key);
            if (result != 0) {
                setError(STS_EINTERNAL, "Failed to delete image object");
                return STS_EINTERNAL;
            }
        }

        // Delete the metadata object
        if (!m_metadata_key.empty()) {
            s3_client->deleteObject(bucket_name, m_metadata_key);
            // Don't fail if metadata deletion fails - it might not exist
        }

        // Mark image as removed
        m_is_open = false;
        m_is_complete = false;
        m_is_pending = false;
        m_size = 0;
        m_current_size = 0;

        std::cout << "VastImage: Successfully removed image " << m_image_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image removal failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

bool VastImage::isComplete() const
{
    return m_is_complete;
}

std::string VastImage::getBucketName() const
{
    // Return the bucket name from the LSU if available
    if (m_lsu) {
        return m_lsu->getBucketName();
    }
    return "";
}

VastS3Client* VastImage::getS3Client()
{
    if (m_lsu) {
        return m_lsu->getS3Client();
    }
    return nullptr;
}

void VastImage::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastImage Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastImage::clearError()
{
    m_last_error = STS_EOK;
    m_last_error_msg.clear();
}

std::string VastImage::generateS3Key()
{
    return "netbackup/" + m_image_name + "/" + std::string(m_image_def.img_date) + "/data";
}

std::string VastImage::generateMetadataKey()
{
    return "netbackup/" + m_image_name + "/" + std::string(m_image_def.img_date) + "/metadata";
}

int VastImage::writeMetadata()
{
    // Write updated metadata to the storage server via REST API
    try {
        // Get REST client from storage server for metadata operations
        VastStorageServer* storage_server = m_lsu ? m_lsu->getStorageServer() : nullptr;
        if (!storage_server) {
            setError(STS_EINTERNAL, "Storage server not available");
            return STS_EINTERNAL;
        }

        VastRestClient* rest_client = storage_server->getRestClient();
        if (!rest_client) {
            setError(STS_EINTERNAL, "REST client not available");
            return STS_EINTERNAL;
        }

        // Update image metadata via REST API
        std::string bucket_name = getBucketName();
        std::string object_key = generateS3Key();
        
        // Convert from sts_image_def_v10_t to ImageDefinition
        ImageDefinition img_def;
        img_def.basename = m_image_def.img_basename;  // Fixed field name
        img_def.date = m_image_def.img_date;         // Fixed field name
        img_def.fulldate = ""; // No direct equivalent in sts_image_def_v10_t
        img_def.size = m_size;  // Use m_size instead of id_size which doesn't exist
        img_def.name = m_image_def.img_basename;    // Fixed field name
        img_def.policy = ""; // No direct equivalent in sts_image_def_v10_t
        
        int result = rest_client->updateImageMetadata(bucket_name, object_key, img_def);
        if (result != STS_EOK) {
            syslog(LOG_ERR, "Failed to update image metadata: %d", result);
            return result;
        }
        
        m_metadata_dirty = false;
        std::cout << "VastImage: Successfully wrote metadata for image " << m_image_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        syslog(LOG_ERR, "Exception in writeMetadata: %s", e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::setSize(sts_uint64_t size)
{
    try {
        if (size == m_current_size) {
            return STS_EOK; // No change needed
        }

        // Update the internal size tracking
        m_current_size = size;
        
        // Mark metadata as dirty to ensure it gets updated
        m_metadata_dirty = true;
        
        // If the image is open for writing, we may need to handle truncation
        if (m_is_open && m_mode == STS_O_WRITE) {
            // For S3 objects, we can't really truncate in place
            // The size will be set when the object is finalized
            std::cout << "VastImage: Size set to " << size << " bytes for image " << m_image_name << std::endl;
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Set size failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::create(int flags)
{
    try {
        // Check if we have a valid LSU
        if (!m_lsu) {
            setError(STS_EINVAL, "No LSU provided");
            return STS_EINVAL;
        }

        // Generate S3 key for the image
        m_s3_key = generateS3Key();
        m_metadata_key = generateMetadataKey();

        // Check if image already exists
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        std::string bucket_name = getBucketName();
        if (bucket_name.empty()) {
            setError(STS_EINTERNAL, "Bucket name not available");
            return STS_EINTERNAL;
        }

        // Check if image already exists
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(bucket_name, m_s3_key, object_info);
        if (result == 0) {
            // Object already exists
            if (!(flags & STS_CIF_FORCEREPLACE)) {
                setError(STS_EEXIST, "Image already exists");
                return STS_EEXIST;
            }
            // Will overwrite - delete existing object first
            s3_client->deleteObject(bucket_name, m_s3_key);
        }

        // Set default size if not already set
        if (m_size == 0) {
            m_size = 0;  // Start with zero size, will grow as data is written
        }
        m_current_size = 0;

        // Initialize multipart upload for large images
        if (flags & STS_CIF_LARGEOBJ) {
            result = initializeMultipartUpload();
            if (result != STS_EOK) {
                return result;
            }
        }

        // Mark the image as pending unless complete flag is set
        m_is_pending = !(flags & STS_CIF_COMPLETE);
        m_is_open = true;
        m_mode = STS_O_WRITE;

        // Update the image definition
        m_image_def.img_flags = 0;
        // Don't use img_filetype as it might not exist in all struct versions
        // m_image_def.img_filetype = STS_FT_DISK;

        // Create initial metadata
        m_metadata_dirty = true;
        if (!(flags & STS_CIF_NOMETA)) {
            saveMetadata();
        }

        std::cout << "VastImage: Successfully created image " << m_image_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image creation failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::open(int mode)
{
    try {
        // Check if we have a valid LSU
        if (!m_lsu) {
            setError(STS_EINVAL, "No LSU provided");
            return STS_EINVAL;
        }

        // Check if image is already open
        if (m_is_open) {
            // Can only reopen if compatible mode
            if (m_mode != mode) {
                setError(STS_EBUSY, "Image already open in a different mode");
                return STS_EBUSY;
            }
            return STS_EOK; // Already open in the requested mode
        }

        // Generate S3 key for the image if not already done
        if (m_s3_key.empty()) {
            m_s3_key = generateS3Key();
        }
        if (m_metadata_key.empty()) {
            m_metadata_key = generateMetadataKey();
        }

        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        std::string bucket_name = getBucketName();
        if (bucket_name.empty()) {
            setError(STS_EINTERNAL, "Bucket name not available");
            return STS_EINTERNAL;
        }

        // Check if image exists
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(bucket_name, m_s3_key, object_info);
        if (result != 0) {
            // Object doesn't exist
            if (mode == STS_O_READ) {
                setError(STS_ENOENT, "Image not found");
                return STS_ENOENT;
            }
            // For write mode, we'll create it when data is written
        } else {
            // Object exists - get size
            m_size = object_info.size;
            m_current_size = object_info.size;
        }

        // Load metadata if exists
        loadMetadata();

        m_is_open = true;
        m_mode = mode;

        std::cout << "VastImage: Successfully opened image " << m_image_name
                  << " in " << (mode == STS_O_READ ? "read" : "write") << " mode" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image open failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::close(int complete_flag, int force_flag)
{
    try {
        if (!m_is_open) {
            return STS_EOK; // Already closed
        }

        int result = STS_EOK;

        // Complete multipart upload if active and in write mode
        if (m_is_open && m_mode == STS_O_WRITE && m_multipart_upload.active) {
            if (complete_flag) {
                result = completeMultipartUpload();
            } else {
                result = abortMultipartUpload();
            }

            if (result != STS_EOK && !force_flag) {
                return result;
            }
        }

        // Update metadata if dirty and completing
        if (m_metadata_dirty && complete_flag) {
            result = saveMetadata();
            if (result != STS_EOK && !force_flag) {
                return result;
            }
        }

        // Update completion state
        if (complete_flag) {
            m_is_complete = true;
            m_is_pending = false;
        }

        // Close the image
        m_is_open = false;

        std::cout << "VastImage: Closed image " << m_image_name
                  << (complete_flag ? " and marked as complete" : "")
                  << (force_flag ? " (forced)" : "") << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image close failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::readImage(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read)
{
    if (!buf || !bytes_read) {
        return STS_EINVAL;
    }
    *bytes_read = 0;

    try {
        // Validate operation mode
        if (!m_is_open || (m_mode != STS_O_READ && m_mode != STS_O_WRITE)) {
            setError(STS_EACCESS, "Image not open for reading");
            return STS_EACCESS;
        }

        // Check bounds
        if (offset >= m_size) {
            // EOF
            return STS_EOK;
        }

        // Adjust length if it would read past end of file
        if (offset + length > m_size) {
            length = m_size - offset;
        }

        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Read from S3
        std::string bucket_name = getBucketName();
        int result = s3_client->getObjectRange(bucket_name, m_s3_key, buf, offset, length, bytes_read);
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to read from S3 object");
            *bytes_read = 0;
            return STS_EINTERNAL;
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image read failed: ") + e.what());
        *bytes_read = 0;
        return STS_EINTERNAL;
    }
}

int VastImage::writeImage(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written)
{
    if (!buf || !bytes_written) {
        return STS_EINVAL;
    }
    *bytes_written = 0;

    try {
        // Validate operation mode
        if (!m_is_open || (m_mode != STS_O_WRITE && m_mode != STS_O_READ)) {
            setError(STS_EACCESS, "Image not open for writing");
            return STS_EACCESS;
        }

        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        std::string bucket_name = getBucketName();
        int result;

        // Check if this is a multipart upload
        if (m_multipart_upload.active) {
            // Use multipart upload API
            int part_number = m_multipart_upload.next_part_number++;
            std::string etag;
            result = s3_client->uploadPart(
                VastS3MultipartUpload{m_multipart_upload.upload_id, m_s3_key, {}, 0},
                part_number, buf, length, etag);
            
            if (result == 0) {
                // Save part info for completion
                VastS3PartInfo part_info;
                part_info.part_number = part_number;
                part_info.etag = etag;
                part_info.size = length;
                m_multipart_upload.parts.push_back(part_info);
                
                *bytes_written = length;
                
                // Update size
                if (offset + length > m_current_size) {
                    m_current_size = offset + length;
                    m_size = m_current_size;
                }
            } else {
                setError(STS_EINTERNAL, "Failed to upload part " + std::to_string(part_number));
                return STS_EINTERNAL;
            }
        } else {
            // Use regular put object for small files or specific offsets
            // This is not efficient for S3 in general, but required for the API
            
            // Read entire object first if it exists and offset > 0
            std::vector<char> full_object;
            
            if (offset > 0) {
                // Need to read existing object
                VastS3ObjectInfo object_info;
                result = s3_client->headObject(bucket_name, m_s3_key, object_info);
                
                if (result == 0 && object_info.size > 0) {
                    // Object exists, read it
                    full_object.resize(object_info.size);
                    sts_uint64_t bytes_read = 0;
                    
                    // Fix the getObject call to match the API signature
                    result = s3_client->getObject(bucket_name, m_s3_key, full_object.data(), 
                                                full_object.size(), 0, &bytes_read);
                    if (result != 0 || bytes_read != object_info.size) {
                        setError(STS_EINTERNAL, "Failed to read existing object for update");
                        return STS_EINTERNAL;
                    }
                }
            }
            
            // Resize buffer if needed to accommodate offset + new data
            if (offset + length > full_object.size()) {
                full_object.resize(offset + length);
            }
            
            // Copy new data into the buffer at offset
            memcpy(full_object.data() + offset, buf, length);
            
            // Upload the full object
            result = s3_client->putObject(bucket_name, m_s3_key, full_object.data(), full_object.size());
            
            if (result == 0) {
                *bytes_written = length;
                
                // Update size
                if (full_object.size() > m_current_size) {
                    m_current_size = full_object.size();
                    m_size = m_current_size;
                }
            } else {
                setError(STS_EINTERNAL, "Failed to write to S3 object");
                return STS_EINTERNAL;
            }
        }
        
        // Mark metadata as dirty
        m_metadata_dirty = true;
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image write failed: ") + e.what());
        *bytes_written = 0;
        return STS_EINTERNAL;
    }
}

int VastImage::flush()
{
    try {
        // For S3, there's no real flush concept, but we can save metadata
        // and complete multipart uploads if they're active
        
        if (!m_is_open) {
            return STS_EOK;
        }
        
        // Complete multipart upload if active
        if (m_multipart_upload.active) {
            int result = completeMultipartUpload();
            if (result != STS_EOK) {
                return result;
            }
            
            // Start a new multipart upload for subsequent writes
            result = initializeMultipartUpload();
            if (result != STS_EOK) {
                return result;
            }
        }
        
        // Save metadata if dirty
        if (m_metadata_dirty) {
            int result = saveMetadata();
            if (result != STS_EOK) {
                return result;
            }
        }
        
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image flush failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::getImageInfo(sts_image_info_v10_t* image_info)
{
    if (!image_info) {
        return STS_EINVAL;
    }

    // Clear the struct
    memset(image_info, 0, sizeof(sts_image_info_v10_t));

    // Copy the image definition
    memcpy(&image_info->imo_def, &m_image_def, sizeof(m_image_def));
    
    // Set LSU name and server name
    strncpy(image_info->imo_lsu.sln_name, m_lsu_name.c_str(), sizeof(image_info->imo_lsu.sln_name) - 1);
    image_info->imo_lsu.sln_name[sizeof(image_info->imo_lsu.sln_name) - 1] = '\0';
    
    if (m_lsu) {
        strncpy(image_info->imo_server, m_lsu->getStorageServer()->getServerName().c_str(), 
                sizeof(image_info->imo_server) - 1);
        image_info->imo_server[sizeof(image_info->imo_server) - 1] = '\0';
    }

    // Set size and block size
    image_info->imo_size = m_size;
    image_info->imo_block_size = m_block_size;
    
    // Set status flags
    image_info->imo_status = 0;
    if (m_is_pending) {
        image_info->imo_status |= STS_II_IMAGE_PENDING;
    }
    if (m_is_complete) {
        image_info->imo_status |= STS_II_IMAGE_CREATED;
    }
    if (m_is_open && m_mode == STS_O_READ) {
        image_info->imo_status |= STS_II_BUSY_READ;
    }
    if (m_is_open && m_mode == STS_O_WRITE) {
        image_info->imo_status |= STS_II_BUSY_WRITE;
    }
    
    return STS_EOK;
}

int VastImage::getImageInfo(sts_image_info_v7_t* image_info)
{
    if (!image_info) {
        return STS_EINVAL;
    }

    // Clear the struct
    memset(image_info, 0, sizeof(sts_image_info_v7_t));

    // Copy the image definition (convert from v10 to v7)
    strncpy(image_info->imo_def.img_basename, m_image_def.img_basename, 
            sizeof(image_info->imo_def.img_basename) - 1);
    image_info->imo_def.img_basename[sizeof(image_info->imo_def.img_basename) - 1] = '\0';
    
    strncpy(image_info->imo_def.img_date, m_image_def.img_date, 
            sizeof(image_info->imo_def.img_date) - 1);
    image_info->imo_def.img_date[sizeof(image_info->imo_def.img_date) - 1] = '\0';
    
    image_info->imo_def.img_saveas = m_image_def.img_saveas;
    image_info->imo_def.img_flags = m_image_def.img_flags;
    
    // Set LSU name and server name
    strncpy(image_info->imo_lsu.sln_name, m_lsu_name.c_str(), sizeof(image_info->imo_lsu.sln_name) - 1);
    image_info->imo_lsu.sln_name[sizeof(image_info->imo_lsu.sln_name) - 1] = '\0';
    
    if (m_lsu) {
        strncpy(image_info->imo_server, m_lsu->getStorageServer()->getServerName().c_str(), 
                sizeof(image_info->imo_server) - 1);
        image_info->imo_server[sizeof(image_info->imo_server) - 1] = '\0';
    }

    // Set size and block size
    image_info->imo_size = m_size;
    image_info->imo_block_size = m_block_size;
    
    // Set status flags
    image_info->imo_status = 0;
    if (m_is_pending) {
        image_info->imo_status |= STS_II_IMAGE_PENDING;
    }
    if (m_is_complete) {
        image_info->imo_status |= STS_II_IMAGE_CREATED;
    }
    if (m_is_open && m_mode == STS_O_READ) {
        image_info->imo_status |= STS_II_BUSY_READ;
    }
    if (m_is_open && m_mode == STS_O_WRITE) {
        image_info->imo_status |= STS_II_BUSY_WRITE;
    }
    
    return STS_EOK;
}

int VastImage::initializeMultipartUpload()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Initialize multipart upload for large images
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->initiateMultipartUpload(bucket_name, object_key, m_multipart_upload.upload_id);
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to initialize multipart upload");
            return STS_EINTERNAL;
        }

        m_multipart_upload.active = true;
        m_multipart_upload.next_part_number = 1;

        std::cout << "VastImage: Multipart upload initialized: " << m_multipart_upload.upload_id << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Multipart upload init failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::completeMultipartUpload()
{
    try {
        if (!m_multipart_upload.active) {
            return STS_EOK; // Already completed or not started
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Complete multipart upload
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->completeMultipartUpload(bucket_name, object_key,
                                                       m_multipart_upload.upload_id,
                                                       m_multipart_upload.parts);
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to complete multipart upload");
            return STS_EINTERNAL;
        }

        m_multipart_upload.active = false;
        m_multipart_upload.upload_id.clear();
        m_multipart_upload.parts.clear();

        std::cout << "VastImage: Multipart upload completed successfully" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Multipart upload completion failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::abortMultipartUpload()
{
    try {
        if (!m_multipart_upload.active) {
            return STS_EOK; // Already aborted or not started
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Abort multipart upload
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->abortMultipartUpload(bucket_name, object_key, m_multipart_upload.upload_id);
        if (result != 0) {
            // Log warning but don't fail - cleanup is best effort
            std::cout << "VastImage: Warning - Failed to abort multipart upload: " << m_multipart_upload.upload_id << std::endl;
        }

        m_multipart_upload.active = false;
        m_multipart_upload.upload_id.clear();
        m_multipart_upload.parts.clear();

        std::cout << "VastImage: Multipart upload aborted" << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Multipart upload abort failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::uploadPart(const void* data, sts_uint64_t size, int part_number)
{
    try {
        if (!m_multipart_upload.active) {
            setError(STS_EINTERNAL, "Multipart upload not active");
            return STS_EINTERNAL;
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Upload part
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        std::string etag;
        int result = s3_client->uploadPart(VastS3MultipartUpload{m_multipart_upload.upload_id, object_key, {}, 0},
                                          part_number, data, size, etag);
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to upload part " + std::to_string(part_number));
            return STS_EINTERNAL;
        }

        // Store part info for completion
        VastS3PartInfo part_info;
        part_info.part_number = part_number;
        part_info.etag = etag;
        m_multipart_upload.parts.push_back(part_info);

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Upload part failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::loadMetadata()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            return STS_EOK; // Can't load without S3 client
        }

        // Generate metadata key
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        // Try to get metadata object
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(bucket_name, metadata_key, object_info);
        if (result != 0) {
            // No metadata object exists yet
            return STS_EOK;
        }

        // In a real implementation, we would:
        // 1. Download the metadata object
        // 2. Parse the metadata (JSON/XML)
        // 3. Update internal metadata structures

        std::cout << "VastImage: Loaded metadata for image " << m_image_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Load metadata failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::saveMetadata()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            m_metadata_dirty = false;
            return STS_EOK; // Can't save without S3 client
        }

        // Generate metadata content (simplified JSON)
        std::string metadata_content = "{\n";
        metadata_content += "  \"image_name\": \"" + m_image_name + "\",\n";
        metadata_content += "  \"image_size\": " + std::to_string(m_current_size) + ",\n";
        metadata_content += "  \"image_date\": \"" + std::string(m_image_def.img_date) + "\",\n";
        metadata_content += "  \"image_flags\": " + std::to_string(m_image_def.img_flags) + ",\n";
        metadata_content += "  \"created_time\": \"" + std::to_string(time(nullptr)) + "\"\n";
        metadata_content += "}";

        // Upload metadata object
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        int result = s3_client->putObject(bucket_name, metadata_key,
                                         metadata_content.data(), metadata_content.size());
        if (result != 0) {
            setError(STS_EINTERNAL, "Failed to save metadata");
            return STS_EINTERNAL;
        }

        m_metadata_dirty = false;
        std::cout << "VastImage: Saved metadata for image " << m_image_name << std::endl;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Save metadata failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::readMetadata(void* buf, sts_uint64_t len, sts_uint64_t offset, sts_uint64_t* bytesread)
{
    if (!buf || !bytesread) {
        return STS_EINVAL;
    }

    try {
        *bytesread = 0;

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            return STS_EOK; // Can't read without S3 client
        }

        // Generate metadata key
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        // Read metadata object with range
        int result = s3_client->getObjectRange(bucket_name, metadata_key, buf, offset, len, bytesread);
        if (result != 0) {
            // Metadata object doesn't exist or read failed
            return STS_EOK;
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Read metadata failed: ") + e.what());
        *bytesread = 0;
        return STS_EINTERNAL;
    }
}

int VastImage::writeMetadata(void* buf, sts_uint64_t len, sts_uint64_t offset, sts_uint64_t* byteswritten)
{
    if (!buf || !byteswritten) {
        return STS_EINVAL;
    }

    try {
        *byteswritten = 0;

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            return STS_EINTERNAL; // Can't write without S3 client
        }

        // For S3, writing at offset requires reading the entire object, modifying it, and re-uploading
        std::vector<char> metadata_content;
        sts_uint64_t metadata_size = 0;

        // Generate metadata key
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        // Get existing metadata if offset > 0
        if (offset > 0) {
            VastS3ObjectInfo object_info;
            int result = s3_client->headObject(bucket_name, metadata_key, object_info);
            if (result == 0 && object_info.size > 0) {
                // Read existing metadata
                metadata_content.resize(object_info.size);
                sts_uint64_t bytes_read = 0;
                
                // Fix the getObject call to match the API signature
                result = s3_client->getObject(bucket_name, metadata_key, metadata_content.data(), 
                                             metadata_content.size(), 0, &bytes_read);
                if (result == 0) {
                    metadata_size = bytes_read;
                }
            }
        }

        // Ensure buffer is large enough for new data
        if (metadata_content.size() < offset + len) {
            metadata_content.resize(offset + len);
        }

        // Copy new data at offset
        memcpy(metadata_content.data() + offset, buf, len);
        if (offset + len > metadata_size) {
            metadata_size = offset + len;
        }

        // Upload updated metadata
        int result = s3_client->putObject(bucket_name, metadata_key, metadata_content.data(), metadata_size);
        if (result != 0) {
            return STS_EINTERNAL;
        }

        *byteswritten = len;
        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Write metadata failed: ") + e.what());
        *byteswritten = 0;
        return STS_EINTERNAL;
    }
}

int VastImage::copyFrom(VastImage* source_image, const std::string& operation_name, int event_flag)
{
    if (!source_image) {
        return STS_EINVAL;
    }

    try {
        // Check if source image is open
        if (!source_image->isOpen()) {
            setError(STS_EINVAL, "Source image not open");
            return STS_EINVAL;
        }

        // Check if this image is open in write mode
        if (!m_is_open || m_mode != STS_O_WRITE) {
            setError(STS_EACCESS, "Target image not open for writing");
            return STS_EACCESS;
        }

        // Get S3 clients
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_EINTERNAL, "S3 client not available");
            return STS_EINTERNAL;
        }

        // Check if source and destination are in the same bucket 
        // (if not, would need to copy between buckets)
        std::string src_bucket = source_image->getBucketName();
        std::string dst_bucket = getBucketName();
        std::string src_key = source_image->getS3Key();
        std::string dst_key = m_s3_key;

        // If same bucket, just use S3 copy object
        if (src_bucket == dst_bucket) {
            int result = s3_client->copyObject(src_bucket, src_key, dst_bucket, dst_key);
            if (result != 0) {
                setError(STS_EINTERNAL, "S3 copy object failed");
                return STS_EINTERNAL;
            }
        } else {
            // Different buckets - copy contents 
            // In a real implementation, this would stream the data or use multipart upload

            // Get source object size
            VastS3ObjectInfo src_info;
            int result = s3_client->headObject(src_bucket, src_key, src_info);
            if (result != 0) {
                setError(STS_EINTERNAL, "Failed to get source object info");
                return STS_EINTERNAL;
            }

            // For small objects, just download then upload
            if (src_info.size < 100 * 1024 * 1024) { // < 100MB
                std::vector<char> buffer(src_info.size);
                sts_uint64_t bytes_read = 0;

                // Fix the getObject call with the correct signature
                result = s3_client->getObject(src_bucket, src_key, buffer.data(), 
                                             buffer.size(), 0, &bytes_read);
                if (result != 0 || bytes_read != src_info.size) {
                    setError(STS_EINTERNAL, "Failed to download source object");
                    return STS_EINTERNAL;
                }

                result = s3_client->putObject(dst_bucket, dst_key, buffer.data(), buffer.size());
                if (result != 0) {
                    setError(STS_EINTERNAL, "Failed to upload to destination");
                    return STS_EINTERNAL;
                }
            } else {
                // For large objects, need streaming or multipart upload
                setError(STS_EINTERNAL, "Large cross-bucket copying not implemented");
                return STS_EINTERNAL;
            }
        }

        // Update size and metadata
        m_size = source_image->getSize();
        m_current_size = m_size;
        m_metadata_dirty = true;

        // Save metadata
        saveMetadata();

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Image copy failed: ") + e.what());
        return STS_EINTERNAL;
    }
}

int VastImage::copyTo(VastImage* target_image, const std::string& operation_name, int event_flag)
{
    if (!target_image) {
        return STS_EINVAL;
    }

    // Delegate to target's copyFrom method
    return target_image->copyFrom(this, operation_name, event_flag);
}

int VastImage::copyExtent(VastImage* dst_image, sts_uint64_t src_offset, sts_uint64_t dst_offset, 
                          sts_uint64_t length, int flags, sts_uint64_t* bytes_copied)
{
    if (!dst_image || !bytes_copied) {
        return STS_EINVAL;
    }

    try {
        *bytes_copied = 0;

        // Check if both images are open
        if (!m_is_open) {
            setError(STS_EINVAL, "Source image not open");
            return STS_EINVAL;
        }

        if (!dst_image->isOpen()) {
            setError(STS_EINVAL, "Destination image not open");
            return STS_EINVAL;
        }

        // Check source bounds
        if (src_offset >= m_size) {
            return STS_EOK; // Nothing to copy
        }

        // Adjust length if it exceeds source bounds
        if (src_offset + length > m_size) {
            length = m_size - src_offset;
        }

        // For simplicity in this example, we'll read from source and write to destination
        // In a real implementation, this could use more efficient mechanisms like server-side copy
        std::vector<char> buffer(length);
        sts_uint64_t bytes_read = 0;

        int result = readImage(buffer.data(), length, src_offset, &bytes_read);
        if (result != STS_EOK) {
            return result;
        }

        if (bytes_read == 0) {
            return STS_EOK; // Nothing read, nothing to copy
        }

        // Write to destination
        result = dst_image->writeImage(buffer.data(), bytes_read, dst_offset, bytes_copied);
        if (result != STS_EOK) {
            return result;
        }

        return STS_EOK;
    }
    catch (const std::exception& e) {
        setError(STS_EINTERNAL, std::string("Copy extent failed: ") + e.what());
        *bytes_copied = 0;
        return STS_EINTERNAL;
    }
}

sts_uint64_t VastImage::calculateOptimalPartSize(sts_uint64_t total_size)
{
    // Calculate optimal part size for multipart upload
    // Amazon S3 limits:
    // - Minimum part size: 5 MB (except last part)
    // - Maximum part size: 5 GB
    // - Maximum parts: 10,000
    
    const sts_uint64_t MIN_PART_SIZE = 5 * 1024 * 1024;
    const sts_uint64_t MAX_PART_SIZE = 5ULL * 1024 * 1024 * 1024;
    const sts_uint64_t MAX_PARTS = 10000;
    
    // Start with minimum part size
    sts_uint64_t part_size = MIN_PART_SIZE;
    
    // If total size is known, calculate optimal part size
    if (total_size > 0) {
        // Aim for at least 100 MB parts for large files to reduce number of parts
        if (total_size > 1024 * 1024 * 1024) {  // > 1 GB
            part_size = 100 * 1024 * 1024;  // 100 MB parts
        }
        
        // Ensure we don't exceed maximum number of parts
        sts_uint64_t min_required_size = (total_size + MAX_PARTS - 1) / MAX_PARTS;
        if (part_size < min_required_size) {
            part_size = min_required_size;
        }
        
        // Round up to nearest MB
        part_size = ((part_size + 1024 * 1024 - 1) / (1024 * 1024)) * (1024 * 1024);
        
        // Ensure part size is within limits
        if (part_size < MIN_PART_SIZE) {
            part_size = MIN_PART_SIZE;
        }
        if (part_size > MAX_PART_SIZE) {
            part_size = MAX_PART_SIZE;
        }
    }
    
    return part_size;
}

int VastImage::validateOperation(int mode)
{
    if (!m_is_open) {
        // Use STS_EINVAL instead of STS_ENOTOPEN which might not be defined in the SDK
        setError(STS_EINVAL, "Image not open");
        return STS_EINVAL;
    }
    
    if ((mode & STS_O_READ) && !(m_mode & STS_O_READ)) {
        setError(STS_EACCESS, "Image not open for reading");
        return STS_EACCESS;
    }
    
    if ((mode & STS_O_WRITE) && !(m_mode & STS_O_WRITE)) {
        setError(STS_EACCESS, "Image not open for writing");
        return STS_EACCESS;
    }
    
    return STS_EOK;
}

void VastImage::updateInfo()
{
    // Update the internal ImageInfo structure with current state
    m_image_info.size = m_current_size;
    m_image_info.name = m_image_name;
    m_image_info.date = std::string(m_image_def.img_date);
    m_image_info.image_path = m_s3_key;
    
    // Update the embedded image definition
    m_image_info.idef.basename = m_image_def.img_basename;
    m_image_info.idef.date = m_image_def.img_date;
    m_image_info.idef.size = m_current_size;
    m_image_info.idef.name = m_image_def.img_basename;
    m_image_info.idef.policy = ""; // Not directly available in sts_image_def_v10_t
    
    // Update fulldate - try to create a more complete date string
    if (m_timestamp > 0) {
        struct tm* timeinfo = gmtime(reinterpret_cast<const time_t*>(&m_timestamp));
        if (timeinfo) {
            char fulldate_buffer[64];
            strftime(fulldate_buffer, sizeof(fulldate_buffer), "%Y-%m-%d %H:%M:%S UTC", timeinfo);
            m_image_info.idef.fulldate = fulldate_buffer;
        } else {
            m_image_info.idef.fulldate = m_image_info.idef.date;
        }
    } else {
        m_image_info.idef.fulldate = m_image_info.idef.date;
    }
}
