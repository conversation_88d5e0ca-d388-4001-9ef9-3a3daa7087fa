/*
 *************************************************************************
 * Vast Data Image Class
 * UPDATED: Now implements our own image interface instead of using sample code
 *************************************************************************
 */

#ifndef _VAST_IMAGE_H_
#define _VAST_IMAGE_H_

// Include SDK headers first
#include "../OST-SDK-11.1.1/src/include/stsi.h"
#include "VastCommonTypes.h"  // Include common types and constants
#include "VastS3Client.h"  // For VastS3PartInfo
#include <string>
#include <memory>
#include <map>

// Forward declarations
class VastLSU;
class VastS3Client;

// Define our own Image interface instead of using sample code
class VastImage {
public:
    VastImage(VastLSU* lsu, const sts_image_def_v10_t* image_def);
    virtual ~VastImage();
    
    // Default constructor and initialization methods needed by the plugin
    VastImage();
    void setName(const std::string& name) { m_image_name = name; }
    void setBasename(const char* basename);
    void setTimestamp(sts_uint64_t timestamp);
    void setLSUName(const std::string& lsu_name);
    void setMasterServer(const char* master_server);
    void setBackupTime(sts_uint64_t time);
    void setCopyNumber(int copy_number);
    void setStreamNumber(int stream_number);
    void setFragmentNumber(int frag_number);
    std::string getName() const { return m_image_name; }

    // Image interface implementation (required by OST SDK)
    virtual ImageInfo* getInfo();
    virtual int write(void* buf, size_t length, size_t offset, sts_uint64_t* num_written);
    virtual size_t read(void* buf, size_t length, size_t offset);
    virtual int remove();

    // Extended interface for compatibility with existing code
    std::string getImageName() const { return m_image_name; }
    std::string getBasename() const { return m_image_name; }
    sts_uint64_t getTimestamp() const { return m_timestamp; }
    std::string getS3Key() const { return m_s3_key; }
    std::string getBucketName() const;
    sts_uint64_t getSize() const { return m_size; }
    sts_uint64_t getBlockSize() const { return m_block_size; }
    
    // Size management
    int setSize(sts_uint64_t new_size);
    
    // Image operations
    int create(int flags);
    int open(int mode);
    int close(int complete_flag, int force_flag);
    
    // Extended I/O operations (different names to avoid conflicts)
    int readImage(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read);
    int writeImage(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written);
    int flush();
    
    // Metadata operations
    int readMetadata(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read);
    int writeMetadata(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written);
    
    // Image information
    int getImageInfo(sts_image_info_v10_t* image_info);
    int getImageInfo(sts_image_info_v7_t* image_info);
    void updateInfo();  // Add missing updateInfo method declaration
    
    // Copy operations
    int copyFrom(VastImage* source_image, const std::string& operation_name, int event_flag);
    int copyTo(VastImage* target_image, const std::string& operation_name, int event_flag);
    int copyExtent(VastImage* dst_image, sts_uint64_t src_offset, sts_uint64_t dst_offset, 
                   sts_uint64_t length, int flags, sts_uint64_t* bytes_copied);
    
    // Status and state
    bool isOpen() const { return m_is_open; }
    bool isPending() const { return m_is_pending; }
    bool isComplete() const;
    int getMode() const { return m_mode; }
    
    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

    // Internal methods
    VastLSU* getLSU() { return m_lsu; }
    const sts_image_def_v10_t* getImageDefinition() const { return &m_image_def; }

private:
    // OST SDK required members
    ImageInfo m_image_info;
    
    // Image definition and properties
    sts_image_def_v10_t m_image_def;
    std::string m_image_name;
    std::string m_s3_key;
    std::string m_metadata_key;
    std::string m_lsu_name;
    
    // Size and block information
    sts_uint64_t m_size;
    sts_uint64_t m_block_size;
    sts_uint64_t m_current_size;
    sts_uint64_t m_timestamp;
    
    // NetBackup specific information
    std::string m_master_server;
    sts_uint64_t m_backup_time;
    int m_copy_number;
    int m_stream_number;
    int m_fragment_number;
    
    // State information
    bool m_is_open;
    bool m_is_pending;
    bool m_is_complete;
    int m_mode;  // STS_MODE_READ, STS_MODE_WRITE, etc.
    
    // Image flags and size tracking
    sts_uint32_t m_image_flags;
    sts_uint64_t m_image_size;
    
    // LSU reference
    VastLSU* m_lsu;
    
    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;
    
    // S3 multipart upload state
    struct MultipartUpload {
        std::string upload_id;
        std::vector<std::string> part_etags;
        std::vector<VastS3PartInfo> parts;
        int next_part_number;
        bool active;

        MultipartUpload() : next_part_number(1), active(false) {}
    };
    MultipartUpload m_multipart_upload;
    
    // Metadata storage
    std::map<std::string, std::string> m_metadata;
    bool m_metadata_dirty;
    
    // Helper methods
    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string generateS3Key();
    std::string generateMetadataKey();
    int initializeMultipartUpload();
    int completeMultipartUpload();
    int abortMultipartUpload();
    int uploadPart(const void* data, sts_uint64_t size, int part_number);
    int loadMetadata();
    int saveMetadata();
    int writeMetadata();
    VastS3Client* getS3Client();
    int validateOperation(int mode);
    sts_uint64_t calculateOptimalPartSize(sts_uint64_t total_size);
    
    void initDefaults();
};

#endif /* _VAST_IMAGE_H_ */
