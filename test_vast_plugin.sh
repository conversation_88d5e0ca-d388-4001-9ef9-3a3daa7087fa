#!/bin/bash

# Vast Data OST Plugin Testing Script
# This script builds and tests the Vast Data OST plugin using the OST SDK pgntester

set -e  # Exit on any error

# Configuration
WORKSPACE_ROOT="/Users/<USER>/workspace/vast"
PLUGIN_DIR="$WORKSPACE_ROOT/VastOSTPlugin"
OST_SDK_DIR="$WORKSPACE_ROOT/OST-SDK-11.1.1"
PGNTESTER_DIR="$OST_SDK_DIR/tools/pgntester"

# Plugin configuration for testing
PLUGIN_PREFIX="vast"
STORAGE_SERVER="vast://test-vms.example.com"
LSU_NAME="test-lsu"
PLATFORM="linuxR_x64"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    echo_info "Checking prerequisites..."
    
    # Check if OST SDK exists
    if [ ! -d "$OST_SDK_DIR" ]; then
        echo_error "OST SDK not found at $OST_SDK_DIR"
        exit 1
    fi
    
    # Check if pgntester exists
    if [ ! -f "$PGNTESTER_DIR/pgntestercli.pl" ]; then
        echo_error "pgntester not found at $PGNTESTER_DIR"
        exit 1
    fi
    
    # Check if pgndriver exists for our platform
    PGNDRIVER="$PGNTESTER_DIR/webapps/ROOT/bin/$PLATFORM/pgndriver"
    if [ ! -f "$PGNDRIVER" ]; then
        echo_error "pgndriver not found at $PGNDRIVER"
        exit 1
    fi
    
    # Check if Perl is available
    if ! command -v perl &> /dev/null; then
        echo_error "Perl is required for pgntester but not found"
        exit 1
    fi
    
    # Check build dependencies
    if ! command -v g++ &> /dev/null; then
        echo_error "g++ compiler not found"
        exit 1
    fi
    
    echo_success "All prerequisites met"
}

# Function to build the plugin
build_plugin() {
    echo_info "Building Vast Data OST Plugin..."
    
    cd "$PLUGIN_DIR"
    
    # Clean previous build
    make clean 2>/dev/null || true
    
    # Check SDK availability
    if ! make check-sdk; then
        echo_error "OST SDK check failed"
        exit 1
    fi
    
    # Build the plugin
    if make; then
        echo_success "Plugin built successfully: $PLUGIN_DIR/libvastost.so"
    else
        echo_error "Plugin build failed"
        exit 1
    fi
    
    # Verify the plugin file exists
    if [ ! -f "$PLUGIN_DIR/libvastost.so" ]; then
        echo_error "Plugin library not found after build"
        exit 1
    fi
    
    # Check plugin symbols
    echo_info "Checking plugin symbols..."
    if nm "$PLUGIN_DIR/libvastost.so" | grep -q "stspi_init"; then
        echo_success "Plugin contains required symbols"
    else
        echo_warning "Plugin may be missing required symbols"
    fi
}

# Function to run basic plugin tests
run_basic_tests() {
    echo_info "Running basic plugin tests..."
    
    cd "$PGNTESTER_DIR"
    
    PLUGIN_PATH="$PLUGIN_DIR/libvastost.so"
    
    # Test 1: Plugin claim test (most basic test)
    echo_info "Running plugin claim test..."
    if perl pgntestercli.pl \
        -l "$PLUGIN_PATH" \
        -sts "$PLUGIN_PREFIX" \
        -storage_server "$STORAGE_SERVER" \
        -lsu "$LSU_NAME" \
        -plat "$PLATFORM" \
        -f claim \
        -verbose 3; then
        echo_success "Plugin claim test passed"
    else
        echo_error "Plugin claim test failed"
        return 1
    fi
}

# Function to run comprehensive tests
run_comprehensive_tests() {
    echo_info "Running comprehensive plugin tests..."
    
    cd "$PGNTESTER_DIR"
    
    PLUGIN_PATH="$PLUGIN_DIR/libvastost.so"
    
    # Create logs directory
    mkdir -p logs
    
    # Run all tests
    echo_info "Running all plugin tests (this may take a while)..."
    if perl pgntestercli.pl \
        -l "$PLUGIN_PATH" \
        -sts "$PLUGIN_PREFIX" \
        -storage_server "$STORAGE_SERVER" \
        -lsu "$LSU_NAME" \
        -plat "$PLATFORM" \
        -verbose 2; then
        echo_success "Comprehensive tests completed"
    else
        echo_warning "Some tests may have failed - check logs for details"
    fi
    
    # Show log location
    LATEST_LOG=$(ls -t logs/pgntestercli.pl* 2>/dev/null | head -1)
    if [ -n "$LATEST_LOG" ]; then
        echo_info "Test log available at: $PGNTESTER_DIR/$LATEST_LOG"
    fi
}

# Function to show test results summary
show_test_summary() {
    echo_info "Test Results Summary:"
    
    cd "$PGNTESTER_DIR"
    LATEST_LOG=$(ls -t logs/pgntestercli.pl* 2>/dev/null | head -1)
    
    if [ -f "$LATEST_LOG" ]; then
        echo_info "Analyzing test results from $LATEST_LOG"
        
        PASSED=$(grep -c "PASSED" "$LATEST_LOG" 2>/dev/null || echo "0")
        FAILED=$(grep -c "FAILED" "$LATEST_LOG" 2>/dev/null || echo "0")
        
        echo_info "Tests Passed: $PASSED"
        echo_info "Tests Failed: $FAILED"
        
        if [ "$FAILED" -eq 0 ]; then
            echo_success "All tests passed!"
        else
            echo_warning "Some tests failed. Check the log for details:"
            echo_info "Failed tests:"
            grep "FAILED" "$LATEST_LOG" | head -10
        fi
    else
        echo_warning "No test log found"
    fi
}

# Main execution
main() {
    echo_info "Starting Vast Data OST Plugin Testing"
    echo_info "======================================"
    
    # Parse command line arguments
    QUICK_TEST=false
    COMPREHENSIVE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --quick)
                QUICK_TEST=true
                shift
                ;;
            --comprehensive)
                COMPREHENSIVE=true
                shift
                ;;
            --help)
                echo "Usage: $0 [--quick|--comprehensive]"
                echo "  --quick         Run only basic claim test"
                echo "  --comprehensive Run all available tests"
                echo "  (no args)       Build plugin and run basic tests"
                exit 0
                ;;
            *)
                echo_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute testing steps
    check_prerequisites
    build_plugin
    
    if [ "$QUICK_TEST" = true ]; then
        run_basic_tests
    elif [ "$COMPREHENSIVE" = true ]; then
        run_comprehensive_tests
        show_test_summary
    else
        # Default: run basic tests
        run_basic_tests
    fi
    
    echo_success "Testing completed!"
}

# Run main function with all arguments
main "$@"
